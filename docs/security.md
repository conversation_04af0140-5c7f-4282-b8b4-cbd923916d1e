# Securing Server

## Security features

To enable security features edit `/etc/sysctl.conf` and do the following

```sh
net.ipv4.conf.default.rp_filter = 1
net.ipv4.conf.all.rp_filter = 1
net.ipv4.conf.all.accept_redirects = 0
net.ipv6.conf.all.accept_redirects = 0
net.ipv4.conf.all.send_redirects = 0
net.ipv4.conf.all.accept_source_route = 0
net.ipv6.conf.all.accept_source_route = 0
net.ipv4.conf.all.log_martians = 1
```

by running `sysctl -p` you can see the changes done to the config

### Prevent IP Spoof /etc/host.conf

Change File to mirror below:

```sh
order bind,hosts
multi on
nospoof on
```

### Install Fail2Ban

```sh
apt install fail2ban
systemctl enable fail2ban
systemctl start fail2ban
```

### Check Listening Ports

```sh
ss -tunlp # netstat -tunlp
```

## Enable automatic updated

install unattended-upgrades

```sh
apt install -y unattended-upgrades
```

set it up

```sh
sudo dpkg-reconfigure --priority=low unattended-upgrades
```

https://www.nuharborsecurity.com/ubuntu-server-hardening-guide-2/


https://ubuntu.com/security/certifications/docs/usg



## Firewall

install ufw as a simple firewall cli

```shell
apt install -y ufw
```

allow ssh port

```shell
ufw limit <random-ssh-port>/tcp
```

and do global blocks

```shell
ufw default deny incoming
ufw default allow outgoing
```

enable firewall

```shell
ufw enable
```

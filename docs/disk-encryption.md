# Debian-Full-Disk-Encryption

# fido2

1. <PERSON><PERSON>st wird Debian mit einer passphrase installiert

Fido2 wird registriert
```sh
systemd-cryptenroll /dev/mmcblk0p3 --fido2-device=auto --fido2-with-user-presence=true
```

crypttab anpassen
```sh
nano /etc/crypttab
```
fido2-device=auto in die vierte Spalte einfügen
```sh
mmcblk0p3_crypt UUID=d43f36f6-22cf-4789-9ec6-99b9e6fece37 none luks,discard,fido2-device=auto
```


Im Anschluss initramfs neu bauen. Dazu:

Executables liegen in /sbin und es scheint nicht exportiert zu sein
```sh
PATH="/sbin:$PATH"
```

jetzt initramfs bauen
```sh
update-initramfs -u
```
> cryptsetup: WARNING: mmcblk0p3_crypt: ignoring unknown option 'fido2-device'

Probiere mit dracut
```sh
apt install dracut
```

initramfs neu generieren:
```sh
dracut --regenerate-all --force
```


https://www.guyrutenberg.com/2022/02/17/unlock-luks-volume-with-a-yubikey/

https://access.redhat.com/documentation/en-us/red_hat_enterprise_linux/8/html/security_hardening/configuring-automated-unlocking-of-encrypted-volumes-using-policy-based-decryption_security-hardening

# Dropbear

https://www.cyberciti.biz/security/how-to-unlock-luks-using-dropbear-ssh-keys-remotely-in-linux/

#### Wifi:
https://github.com/fangfufu/wifi-on-debian-initramfs/tree/master
https://gist.github.com/telenieko/d17544fc7e4b347beffa87252393384c
https://www.marcfargas.com/2017/12/enable-wireless-networks-in-debian-initramfs/

# LUKS - cryptsetup

Zurst sbin exportieren:
```sh
PATH="/sbin:$PATH"
```

#### Neues Password
Das Password kann wie folgt geändert werden:
```sh
cryptsetup luksChangeKey /dev/sda2
```

Oder über `systemd-cryptenroll`

```sh
systemd-cryptenroll /dev/sda2 --password
```

Das alte Passwort löschen (sollte in slot 0 sein)
```sh
systemd-cryptenroll /dev/sda2 --wipe-slot=0
```

#### Weiteres Password hinzufüge
Weiteres Passwort hinzufügen:
```sh
cryptsetup luksChangeKey /dev/sda2
```

Oder über `systemd-cryptenroll`
```sh
systemd-cryptenroll /dev/sda2 --password
```

#### luksDump

```sh
cryptsetup luksDump /dev/sda2
```


https://www.enricozini.org/blog/2015/debian/crypttab-reuse-passwords/
https://lists.debian.org/debian-kernel/2014/01/msg00218.html

https://command-not-found.com/keyctl
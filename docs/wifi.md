# Enable Wi-Fi on the server
First check if the wi-fi adater is active by running:

```sh
ip a
```

or check the pci devices an look for the network card

```sh
lspci
```

you can also identity the wifi-network adapter by running:

```sh
ls /sys/class/net/
```

Next, navigate to the `/etc/netplan` directory and locate the appropriate Netplan configuration files.

```sh
ls /etc/netplan/
```

Edit the Netplan configuration file:

```sh
sudoedit /etc/netplan/00-installer-config.yaml 
```

and insert the following configuration stanza while replacing the `SSID-NAME-HERE` and `PASSWORD-HERE` with your SSID network name and password:

```yaml

	wifis:
        wlp1s0:
            access-points:
                "SSID-NAME-HERE":
                    password: "PASSWORD-HERE"
            dhcp4: true
```

Once ready, apply the changes and connect to your wireless interface by executing the bellow command:

```sh
netplan apply
```

Alternatively, if you run into some issues execute:

```sh
netplan --debug apply
```

### Set default Gateway

```sh
sudo route add default gw <ip>
```

and set default adapter for DNS

```sh
resolvectl dns wlp1s0
```

remove default gateway

```sh
sudo route del default gw ************
```

### Set WiFi as default connection
in case it din't work

```sh
sudo dhclient wlp1s0
```

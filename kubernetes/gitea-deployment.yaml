---
apiVersion: v1
kind: Namespace
metadata:
  name: gitea
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: gitea-data-pvc
  namespace: gitea
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: gitea-config
  namespace: gitea
data:
  ssh-domain: gitea.home.lab
  domain: gitea.home.lab
  root-domain: https://gitea.home.lab
  ssh-port: "22"
  enable-notify-mail: "true"
  disable-registration: "true"
  disable-gravatar: "true"
  disable-openid-signin: "false"
  disable-openid-signup: "false"
  enable-auto-registration: "true"
  update-avatar: "true"
  register-email-confirm: "true"
  database-type: mysql
  database-host: gitea-mysql:3306
  database-name: gitea
  database-user: gitea
  database-password: gitea
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gitea-deployment
  namespace: gitea
  labels:
    app: gitea
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gitea
  template:
    metadata:
      labels:
        app: gitea
    spec:
      containers:
        - name: gitea
          image: docker.gitea.com/gitea:1.24.3-rootless
          ports:
            - containerPort: 3000
              name: web
            - containerPort: 22
              name: ssh
          volumeMounts:
            - name: gitea-data-pvc
              mountPath: /var/lib/gitea
            - name: gitea-timezone
              mountPath: /etc/timezone
            - name: gitea-localhost
              mountPath: /etc/localtime
          env:
            - name: USER_GID
              value: "1000"
            - name: USER_UID
              value: "1000"
            - name: START_SSH_SERVER
              value: "true"
            - name: SSH_LISTEN_PORT
              valueFrom:
                configMapKeyRef:
                  name: gitea-config
                  key: ssh-port
            - name: GITEA__server__SSH_DOMAIN
              valueFrom:
                configMapKeyRef:
                  name: gitea-config
                  key: ssh-domain 
            - name: GITEA__server__DOMAIN
              valueFrom:
                configMapKeyRef:
                  name: gitea-config
                  key: domain
            - name: GITEA__server__ROOT_URL
              valueFrom:
                configMapKeyRef:
                  name: gitea-config
                  key: root-domain
            - name: GITEA__server__SSH_PORT
              valueFrom:
                configMapKeyRef:
                  name: gitea-config
                  key: ssh-port
            - name: GITEA__service__ENABLE_NOTIFY_MAIL
              valueFrom:
                configMapKeyRef:
                  name: gitea-config
                  key: enable-notify-mail
            - name: GITEA__service__DISABLE_REGISTRATION
              valueFrom:
                configMapKeyRef:
                  name: gitea-config
                  key: disable-registration
            - name: GITEA__picture__DISABLE_GRAVATAR
              valueFrom:
                configMapKeyRef:
                  name: gitea-config
                  key: disable-gravatar
            - name: GITEA__openid__ENABLE_OPENID_SIGNIN
              valueFrom:
                configMapKeyRef:
                  name: gitea-config
                  key: disable-openid-signin
            - name: GITEA__openid__ENABLE_OPENID_SIGNUP
              valueFrom:
                configMapKeyRef:
                  name: gitea-config
                  key: disable-openid-signup
            - name: GITEA__oauth2_client__ENABLE_AUTO_REGISTRATION
              valueFrom:
                configMapKeyRef:
                  name: gitea-config
                  key: enable-auto-registration
            - name: GITEA__oauth2_client__UPDATE_AVATAR
              valueFrom:
                configMapKeyRef:
                  name: gitea-config
                  key: update-avatar
            - name: GITEA__oauth2_client__REGISTER_EMAIL_CONFIRM
              valueFrom:
                configMapKeyRef:
                  name: gitea-config
                  key: register-email-confirm
              
          livenessProbe:
            httpGet:
              path: /
              port: 3000
            initialDelaySeconds: 60
            timeoutSeconds: 5
            periodSeconds: 60
          readinessProbe:
            httpGet:
              path: /
              port: 3000
            initialDelaySeconds: 0
            timeoutSeconds: 2
            periodSeconds: 1
      volumes:
        - name: gitea-timezone
          hostPath:
            path: /etc/timezone
            type: File
        - name: gitea-localhost
          hostPath:
            path: /etc/localtime
            type: File
        - name: gitea-data-pvc
          persistentVolumeClaim:
            claimName: gitea-data-pvc 

---
apiVersion: v1
kind: Service
metadata:
  name: gitea-service
spec:
  selector:
    app: gitea
  ports:
    - protocol: TCP
      port: 8000
      targetPort: 3000
  type: LoadBalancer

# Ansible

This project aims to script the entire infrastructure used in the home lab.
All services and projects are hosted with [Docker](https://docs.docker.com/get-started/overview/) and Debian as host OS.

## Prerequisites

For local development it requires:

- [Vagrant](https://developer.hashicorp.com/vagrant/docs)
- [ansible](https://docs.ansible.com/)

#### Ansible Modules

Ansible modules used:

- [docker_container](https://docs.ansible.com/ansible/latest/collections/community/docker/docker_container_module.html)
- [docker_volume](https://docs.ansible.com/ansible/latest/collections/community/docker/docker_volume_module.html)
- [docker_network](https://docs.ansible.com/ansible/latest/collections/community/docker/docker_network_module.html)

<br />

Install dependencies:

```shell
ansible-galaxy install -r requirements.yml
```

<br/>

## Machine

The **home-lab** project is setup to run with docker on [Minis Forum UM250](./docs/machine.md) and provisioned with [docker playbook](./playbooks/docker.playbook.yaml).
All drives are encrypted and to avoid to hook up the machine to the monitor [beardrop](./playbooks/beardrop.playbook.yaml) is used.

After the machine has been restarted there is a 15 minutes time window to login and enter the encryption password.

```bash
ssh root@**************
```

after the drives are decrypted a regular ssh-connection can be established.

```bash
ssh docker@************** -p 948
```

### SSH Config

Alternatively you con copy the following ssh config and make ssh connection by host name

```bash
Host debian
  User docker
  HostName **************
  Port 948

Host debian-boot
  User root
  HostName **************
```

> ssh config file is located at `~/.ssh/config`.

Now it is as simple as:

```bash
ssh debian
```

to establish a ssh connection with `debian` server or

```bash
ssh debian-boot
```

to establish a connection with the machine and enter the decryption key

### Docker Playbook

For provisioning the machine with `docker playbook` run:

```bash
ansible-playbook --vault-password-file=~/.ansible-vault.key playbooks/docker.playbook.yaml --limit debian
```

> where `debian` is the hostname of the machine. This needs to be set up in `inventory.yaml` first

### Monitoring Playbook

For provisioning the machine with `monitoring playbook`run:

```bash
ansible-playbook --vault-password-file=~/.ansible-vault.key playbooks/monitoring.playbook.yaml --limit debian
```

### Smart-Home Playbook

For provisioning the machine with `smart-home playbook` run:

```bash
ansible-playbook --vault-password-file=~/.ansible-vault.key playbooks/smart-home.playbook.yaml --limit debian
```

<br />

## Services

Services this scripts creates when is executed

- [Gitea](./playbooks/roles/gitea/README.md)
- [Grafana](./playbooks/roles/grafana/README.md)
- [Home Assistant](./playbooks/roles/home-assistant/README.md)
- [Influxdb](./playbooks/roles/influxdb/README.md)
- [Mosquitto](./playbooks/roles/mosquitto/README.md)
- [Nextcloud](./playbooks/roles/nextcloud/README.md)
- [Pairdrop](./playbooks/roles/pairdrop/README.md)
- [Pihole](./playbooks/roles/pihole/README.md)
- [Portainer](./playbooks/roles/portainer/README.md)
- [Stirling-PDF](./playbooks/roles/stirling-pdf/README.md)
- [Traefik](./playbooks/roles/traefik/README.md)
- [Vaultwarden](./playbooks/roles/vaultwarden/README.md)
- [Zigbee2Mqtt](./playbooks/roles/zigbee2mqtt/README.md)
- [Zitadel](./playbooks/roles/zitadel/README.md)

<br />

## Known todo's

- ftp client for scanned documents and sync it with Nextcloud with [external storage plugin](https://apps.nextcloud.com/apps/files_external_ethswarm)
- cockroachdb backups - check [docs](https://www.cockroachlabs.com/docs/stable/backup-and-restore-overview)
- Vaultwarden SSO - [Waiting for PR be to merged](https://github.com/dani-garcia/vaultwarden/pull/3899)
- vulnerabilities scan
- security

### More inspiration

- [awesome selfhosted](https://github.com/awesome-selfhosted/awesome-selfhosted)
- [the book of secret knowledge](https://github.com/trimstray/the-book-of-secret-knowledge)
- [cloudron](https://www.cloudron.io/store/index.html) provides a list with self hostable software
- [Jim's Garage](https://github.com/JamesTurland/JimsGarage/tree/main)

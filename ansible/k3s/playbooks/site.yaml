# code: language=ansible
#
# Requirements:
# 
# install kubectl locally to interact with kubernetes cluster
# https://kubernetes.io/de/docs/tasks/tools/install-kubectl/
#
# usage: ansible-playbook --vault-password-file=~/.ansible-vault.key playbooks/k3s.playbook.yaml --limit debian
---
- name: Install k3s
  hosts: all
  gather_facts: true

  pre_tasks:
    - name: update all packages before running any other task
      become: true
      package:
        update_cache: true
        upgrade: "dist"
        autoremove: yes
        autoclean: yes

  roles:
    - longhorn

  tasks:
    - name: install curl if curl is missing
      become: true
      package:
        name: curl
        state: latest

    - debug: 
        msg: "ip = {{ansible_all_ipv4_addresses[0]}}"

    - name: install k3s
      become: true
      shell: |
        curl -sfL https://get.k3s.io | sh -s - server --node-ip={{ansible_all_ipv4_addresses[0]}} --node-external-ip={{ansible_all_ipv4_addresses[0]}}

    - name: verify k3s is running
      become: true
      shell: k3s kubectl get nodes

    - name: copy kubectl config to vagrant directory
      become: true
      copy:
        src: /etc/rancher/k3s/k3s.yaml
        dest: /vagrant/config
        remote_src: true

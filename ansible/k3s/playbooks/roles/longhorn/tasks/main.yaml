---
- name: install prerequisites
  become: true
  package:
    name:
      - open-iscsi
      - cryptsetup
      - nfs-common
      - dmsetup
    state: latest

- name: start iscsi service
  become: true
  service:
    name: iscsid
    state: started
    enabled: yes

- name: check if dm_crypt module is loaded
  shell: lsmod | grep dm_crypt
  register: dm_crypt_module
  changed_when: dm_crypt_module.rc > 0
  ignore_errors: true

- name: load dm_crypt module
  when: dm_crypt_module.rc > 0
  become: true
  shell: modprobe dm_crypt
  ignore_errors: true

# code: language=ansible
#
# usage: ansible-playbook --vault-password-file=~/.ansible-vault.key playbooks/monitoring.playbook.yaml --limit debian
---
- name: Install monitoring as docker-services
  hosts: all
  gather_facts: true

  pre_tasks:
    - name: update all packages before running any other task
      become: true
      package:
        update_cache: true
        upgrade: "dist"
        autoremove: yes
        autoclean: yes

  roles:
    - docker
    - traefik
    - portainer
    - influxdb
    - grafana

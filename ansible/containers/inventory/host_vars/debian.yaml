# code: language=ansible
---
home_lab_base_url: catslab.duckdns.org

traefik_enable_mqtt_forwarding: true
traefik_enable_ssh_forwarding: true

influxdb_monitor_internet_speed_interval: 60m

zigbee2mqtt_mqtt_host: mqtt://mqtt.catslab.duckdns.org:1883
zigbee2mqtt_mqtt_zigbee_device: /dev/serial/by-id/usb-ITead_Sonoff_Zigbee_3.0_USB_Dongle_Plus_24f4ef441519ec11b5ac36cc47486eb0-if00-port0

home_assistant_zigbee_device: /dev/serial/by-id/usb-ITead_Sonoff_Zigbee_3.0_USB_Dongle_Plus_24f4ef441519ec11b5ac36cc47486eb0-if00-port0

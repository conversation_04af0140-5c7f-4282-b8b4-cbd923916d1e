# code: language=ansible
---
home_lab_base_url: home.localhost

# traefik ACME
traefik_acme_email: <EMAIL>
traefik_duckdns_token: 0f233aec-adfe-4444-b2c0-f1158f596740

# Wifi settings
wifi_ssid: My Wi-Fi
wifi_password: "{{ lookup('file', 'wifi-password') }}"
wifi_use_wifi: false

# SMTP settings
smtp_host: smtp-mail.outlook.com
smtp_port: 587
smtp_username: <EMAIL>
smtp_password: "{{ lookup('file', 'smtp-password') }}"
smtp_encryption: starttls

# Default network config
network_netmask: "{{ ansible_default_ipv4.netmask }}"
network_netmask_prefix: "{{ ansible_default_ipv4.prefix }}"
network_gateway: "{{ ansible_default_ipv4.gateway }}"
network_dns_nameserver: "{{ network_gateway }}"

# Grafana

| Environment     | URL                                                                                          |
| --------------- | -------------------------------------------------------------------------------------------- |
| local (Vagrant) | [https://grafana.reuchlinstrasse.duckdns.org/](https://grafana.reuchlinstrasse.duckdns.org/) |
| prod            | [https://grafana.catslab.duckdns.org/](https://grafana.catslab.duckdns.org/)                 |

<br />

## Installation

- [docker image](https://hub.docker.com/r/grafana/grafana)
- [installation instructions](https://grafana.com/docs/grafana/latest/setup-grafana/installation/docker/)

> default username and password is `admin:admin`.

## Versions

| Docker image    | Version                                                                                                                                       |
| --------------- | --------------------------------------------------------------------------------------------------------------------------------------------- |
| grafana/grafana | [11.4.0](https://hub.docker.com/layers/grafana/grafana/11.4.0/images/sha256-8d938a1c52b018c60cb3583657e038054387aa18a74f09a865c99a522481f7ac) |

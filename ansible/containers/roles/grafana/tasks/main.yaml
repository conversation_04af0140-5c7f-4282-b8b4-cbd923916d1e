# code: language=ansible
---
- name: create docker volume for grafan
  docker_volume:
    name: grafana-volume
    state: present
  tags:
    - grafana
    - monitoring

- name: create docker container for grafana
  docker_container:
    name: grafana
    image: "grafana/grafana:{{ grafana_version | mandatory }}"
    state: started
    restart_policy: unless-stopped
    volumes:
      - grafana-volume:/var/lib/grafana
    networks:
      - name: "{{ traefik_proxy_network_name | mandatory }}"
    labels:
      backup.enable: "true"
      traefik.enable: "true"

      traefik.http.routers.grafana.entrypoints: web
      traefik.http.routers.grafana.rule: "Host(`{{ grafana_url | mandatory }}`)"
      traefik.http.routers.grafana.middlewares: grafana-https

      traefik.http.routers.grafana-https.tls: "true"
      traefik.http.routers.grafana-https.tls.certresolver: letsencrypt
      traefik.http.routers.grafana-https.entrypoints: "web-secured"
      traefik.http.routers.grafana-https.rule: "Host(`{{ grafana_url }}`)"
      traefik.http.middlewares.grafana-https.redirectscheme.scheme: https
      traefik.http.services.grafana-https.loadbalancer.server.port: "3000"
  tags:
    - grafana
    - monitoring

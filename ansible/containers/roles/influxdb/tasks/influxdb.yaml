# code: language=ansible
---
- name: "create config directory for {{ influxdb_container_name | mandatory }}"
  become: true
  file:
    state: directory
    path: "{{ influxdb_config_directory | mandatory }}"
    owner: "{{ ansible_user_id }}"
    group: "{{ ansible_user_id }}"

- name: "copy influxdb config to {{ influxdb_config_directory }}"
  copy:
    src: config.yml
    dest: "{{ influxdb_config_directory }}"

- name: create a docker volume for influxdb
  docker_volume:
    name: "{{ item }}"
    state: present
  loop:
    - "{{ influxdb_container_name }}-volume"
    - "{{ influxdb_container_name }}-config-volume"

- name: "create {{ influxdb_container_name }} in a docker container"
  docker_container:
    name: "{{ influxdb_container_name }}"
    image: "influxdb:{{ influxdb_version }}"
    state: started
    restart_policy: unless-stopped
    volumes:
      - "{{ influxdb_container_name }}-config-volume:/etc/influxdb2"
      - "{{ influxdb_container_name }}-volume:/var/lib/influxdb2"
    networks:
      - name: "{{ traefik_proxy_network_name | mandatory }}"
    env:
      DOCKER_INFLUXDB_INIT_MODE: setup
      DOCKER_INFLUXDB_INIT_USERNAME: "{{ influxdb_init_username }}"
      DOCKER_INFLUXDB_INIT_PASSWORD: "{{ influxdb_init_password }}"
      DOCKER_INFLUXDB_INIT_ORG: "{{ influxdb_init_org | mandatory }}"
      DOCKER_INFLUXDB_INIT_BUCKET: "{{ influxdb_init_bucket | mandatory }}"
      DOCKER_INFLUXDB_INIT_RETENTION: "{{ influxdb_init_bucket_retention }}"

    labels:
      backup.enable: "true"
      traefik.enable: "true"

      traefik.http.routers.influxdb.entrypoints: web
      traefik.http.routers.influxdb.rule: "Host(`{{ influxdb_url | mandatory }}`)"
      traefik.http.routers.influxdb.middlewares: influxdb-https

      traefik.http.routers.influxdb-https.tls: "true"
      traefik.http.routers.influxdb-https.tls.certresolver: letsencrypt
      traefik.http.routers.influxdb-https.entrypoints: "web-secured"
      traefik.http.routers.influxdb-https.rule: "Host(`{{ influxdb_url }}`)"
      traefik.http.middlewares.influxdb-https.redirectscheme.scheme: https
      traefik.http.services.influxdb-https.loadbalancer.server.port: "8086"
  register: influxdb_container

- pause:
    seconds: 5
  when: influxdb_container.changed

- name: check if the init bucket user exists
  shell: "docker exec -it {{ influxdb_container_name }} influx user list | grep {{ influxdb_init_bucket_username }}"
  register: influxdb_init_bucket_user_exists
  changed_when: influxdb_init_bucket_user_exists.stdout == ""
  ignore_errors: true

- name: create user for init bucket
  when: influxdb_init_bucket_user_exists.changed
  community.docker.docker_container_exec:
    container: "{{ influxdb_container_name }}"
    argv:
      - influx
      - user
      - create
      - -o
      - "{{ influxdb_init_org }}"
      - -n
      - "{{ influxdb_init_bucket_username }}"
      - -p
      - "{{ influxdb_init_bucket_password }}"

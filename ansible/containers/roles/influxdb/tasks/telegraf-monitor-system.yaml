# code: language=ansible
---
- stat:
    path: /etc/apt/trusted.gpg.d/influxdata-archive_compat.gpg
  register: repository_signature_exists
  ignore_errors: true
  become: true

# taken from https://docs.influxdata.com/telegraf/v1/install/
- name: add InfluxData repository
  become: true
  when: ansible_os_family == "Debian" and not repository_signature_exists.stat.exists
  block:
    - name: install curl if it is missing
      package:
        name: curl
        state: latest

    - name: add repository key
      shell: |
        curl -s https://repos.influxdata.com/influxdata-archive_compat.key > influxdata-archive_compat.key && \
        echo '393e8779c89ac8d958f81f942f9ad7fb82a25e133faddaf92e15b16e6ac9ce4c influxdata-archive_compat.key' | sha256sum -c && cat influxdata-archive_compat.key | gpg --dearmor | tee /etc/apt/trusted.gpg.d/influxdata-archive_compat.gpg > /dev/null && \
        echo 'deb [signed-by=/etc/apt/trusted.gpg.d/influxdata-archive_compat.gpg] https://repos.influxdata.com/debian stable main' | tee /etc/apt/sources.list.d/influxdata.list

    - name: remove repository key
      file:
        path: influxdata-archive_compat.key
        state: absent

    - name: remove curl
      package:
        name: curl
        state: absent

- name: install telegraf
  become: true
  package:
    update_cache: true
    name: telegraf
    state: latest

- name: check if token already exists
  shell: "docker exec -it {{ influxdb_container_name }} influx auth list | grep {{ influxdb_monitor_system_token_name }} | awk -F'\t' '{print($3)}'"
  register: influxdb_telegraf_token
  changed_when: influxdb_telegraf_token.stdout == ""

- name: setup telegraf locally
  when: influxdb_telegraf_token.changed
  block:
    - name: find {{ influxdb_init_bucket }} bucket id
      shell: "docker exec -it {{ influxdb_container_name }} influx bucket list | grep {{ influxdb_init_bucket }} | awk '{print($1)}'"
      register: influxdb_bucket_id

    - name: create auth tocken for telegraf
      community.docker.docker_container_exec:
        container: "{{ influxdb_container_name }}"
        argv:
          - influx
          - auth
          - create
          - --user
          - "{{ influxdb_init_bucket_username }}"
          - --org
          - "{{ influxdb_init_org }}"
          - --read-bucket
          - "{{ influxdb_bucket_id.stdout }}"
          - --write-bucket
          - "{{ influxdb_bucket_id.stdout }}"
          - --description
          - "{{ influxdb_monitor_system_token_name }}"

- name: "find {{ influxdb_monitor_system_token_name }} token"
  shell: "docker exec -it {{ influxdb_container_name }} influx auth list | grep {{ influxdb_monitor_system_token_name }} | awk -F'\t' '{print($3)}'"
  register: influxdb_telegraf_token
  changed_when: influxdb_telegraf_token.rc > 0

- name: copy telegraf config
  become: true
  template:
    src: telegraf-monitor-system.conf.j2
    dest: /etc/telegraf/telegraf.d/system-monitor.conf
  vars:
    influxdb_telegraf_host_token: "{{ influxdb_telegraf_token.stdout | trim }}"
  notify: restart telegraf

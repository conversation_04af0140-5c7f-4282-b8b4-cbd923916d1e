# Influxdb

`Influxdb` is a real-time insights from any time series data and is used to store data from monitoring and data produced from sensors in my smart-home solution

| Environment     | URL                                                                                        |
| --------------- | ------------------------------------------------------------------------------------------ |
| local (Vagrant) | [https://influx.reuchlinstrasse.duckdns.org/](https://influx.reuchlinstrasse.duckdns.org/) |
| prod            | [https://influx.catslab.duckdns.org/](https://influx.catslab.duckdns.org/)                 |

<br />

- [docker image](https://hub.docker.com/_/influxdb)
- [integration docs](https://docs.influxdata.com/platform/integrations/docker/#Copyright)
- [docs](https://docs.influxdata.com/influxdb/v2/)
- [influx cli](https://docs.influxdata.com/influxdb/v2/admin/)

## Versions

| Docker image | Version                                                                                                                                        |
| ------------ | ---------------------------------------------------------------------------------------------------------------------------------------------- |
| influxdb     | [2.7.11](https://hub.docker.com/layers/library/influxdb/2.7.11/images/sha256-813cce36a766ed3e3280b68b806591e64bbe9178c9c0a9b2ed7043aefd51695a) |

## Telegraf

- [docker image](https://hub.docker.com/_/telegraf)
- [telegraf best practices](https://www.influxdata.com/blog/telegraf-best-practices/)

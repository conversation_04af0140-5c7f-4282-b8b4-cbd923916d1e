# code: language=ansible
---
influxdb_version: 2.7.11
influxdb_url: "influx.{{ home_lab_base_url }}"

influxdb_container_name: influxdb

influxdb_config_directory: /var/influxdb

influxdb_init_org: home-lab
influxdb_init_bucket: system
influxdb_init_bucket_retention: 1d

influxdb_init_bucket_username: "{{ influxdb_init_bucket }}-user"

influxdb_monitor_system: true
influxdb_monitor_system_token_name: telegraf-debian

influxdb_monitor_internet_speed: true
influxdb_monitor_internet_speed_interval: 15m

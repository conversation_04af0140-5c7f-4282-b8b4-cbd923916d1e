# Nextcloud

[Nextcloud](https://nextcloud.com/de/) self hosted cloud with the goal to replace google cloud.

| Environment     | URL                                                                                              |
| --------------- | ------------------------------------------------------------------------------------------------ |
| local (Vagrant) | [https://nextcloud.reuchlinstrasse.duckdns.org/](https://nextcloud.reuchlinstrasse.duckdns.org/) |
| prod            | [https://nextcloud..catslab.duckdns.org/](https://nextcloud..catslab.duckdns.org/)               |

- [Apps store](https://apps.nextcloud.com/)

## Installation

`Nextcloud` is installed together with `redis` and `mariadb`. There is an ansible role [nextcloud](../../playbooks/roles/nextcloud/tasks/main.yaml) for quick installation.

- [source code](https://github.com/nextcloud)
- [docker image](https://hub.docker.com/_/nextcloud)
- [docs](https://docs.nextcloud.com/)
- [latest developer manual](https://docs.nextcloud.com/server/latest/developer_manual/)

### Redis

- [source code](https://github.com/docker-library/redis)
- [docker image](https://hub.docker.com/_/redis)

## Onlyoffice

- [source code](https://github.com/ONLYOFFICE/DocumentServer)
- [docker image](https://hub.docker.com/r/onlyoffice/documentserver)

## Versions

| Docker Images             | Version                                                                                                                                                                         |
| ------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| redis                     | [7.2.4-alpine3.19](https://hub.docker.com/layers/library/redis/7.2.4-alpine3.19/images/sha256-3487aa5cf06dceb38202b06bba45b6e6d8a92288848698a6518eee5f63a293a3?context=explore) |
| mariadb                   | [10.6](https://hub.docker.com/layers/library/mariadb/10.6/images/sha256-4763670a35086ae1f2b1372912e9cf3bf576be34f0c9858d008ba10f7c319c43?context=explore)                       |
| nextcloud                 | [30.0.4](https://hub.docker.com/layers/library/nextcloud/30.0.4/images/sha256-61944a4ed4178f0df60f2bed4989492f266db2e7730977dfbc006d388d733b61)                                 |
| onlyoffice/documentserver | [8.2.2](https://hub.docker.com/layers/onlyoffice/documentserver/8.2.2/images/sha256-507384301fb754407308bf329b60f21927866955f33fb3c073f71b8131184ce3)                           |

## Plugins

Some default plugins are installed an configured.

| Plugin                                                     | Comment                                                                                                                                                                              |
| ---------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| [contacts](https://apps.nextcloud.com/apps/contacts)       | The Nextcloud contacts app is a user interface for Nextcloud's CardDAV server. [Nextcloud configuration](#nextcloud)                                                                 |
| [calendar](https://apps.nextcloud.com/apps/calendar)       | The Calendar app is a user interface for Nextcloud's CalDAV server. [Nextcloud configuration](#nextcloud)                                                                            |
| [deck](https://apps.nextcloud.com/apps/deck)               | Deck is a kanban style organization tool aimed at personal planning and project organization for teams. [Deck configuration](#deck)                                                  |
| [recognize](https://apps.nextcloud.com/apps/recognize)     | Smart media tagging and face recognition with on-premises machine learning models. [config values](https://github.com/nextcloud/recognize/blob/main/lib/Service/SettingsService.php) |
| [news](https://apps.nextcloud.com/apps/news)               | `not migrated to newest version yet`                                                                                                                                                 |
| [maps](https://apps.nextcloud.com/apps/maps)               | Open Street View Map self hosted.                                                                                                                                                    |
| [notes](https://apps.nextcloud.com/apps/notes)             | Taking notes in Markdown format. [Notes configuration](#notes)                                                                                                                       |
| [sociallogin](https://apps.nextcloud.com/apps/sociallogin) | SSO configuration for Nextcloud. Is configured to use Zitadel [Social Login configuration](#social-login)                                                                            |
| [cookbook](https://apps.nextcloud.com/apps/cookbook)       | Manage recipes in Nextcloud and sync it with a smart phone [Cookbook configuration](#cookbook)                                                                                       |
| [bookmarks](https://apps.nextcloud.com/apps/bookmarks)     | Sync bookmarks between browsers and devices [Bookmarks configuration](#bookmarks)                                                                                                    |

## Plugins to research

- [epubreader](https://apps.nextcloud.com/apps/epubreader)
- [external storage](https://apps.nextcloud.com/apps/files_external_ethswarm)
- [facerecognition](https://apps.nextcloud.com/apps/facerecognition)
- news sync with nextcloud and keep it up to date with [news-updater](https://github.com/nextcloud/news-updater)

- [nextcloud-all-in-one](https://github.com/nextcloud/all-in-one#nextcloud-all-in-one)

## Maintenance:

https://docs.nextcloud.com/server/latest/admin_manual/configuration_server/occ_command.html#status-return-code

#### Clear error log

While nextcloud set up sometimes errors occur and after all errors are resolved the error log needs to be cleaned manually. The error in protocol-tab are parsed from log file located at `/var/www/html/data/nextcloud.log`. To clear error logs run the following command:

```shell
docker exec -u www-data nextcloud truncate /var/www/html/data/nextcloud.log --size 0
```

#### All logs

`docker logs -f nextcloud` does not contain all logs especially not the error logs. To access all available logs for nextcloud run:

```shell
docker exec -it nextcloud tail -f /var/www/html/data/nextcloud.log
```

## Apps

This section describes quickly the configuration and usage of installed Nextcloud Apps

### Nextcloud

Nextcloud provides couple app to work together with the Webapp.

| Platform | App                                                                                                                                                           |
| -------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Desktop  | [Nextcloud Client](https://nextcloud.com/de/install/)                                                                                                         |
| Mobile   | [Play Store](https://play.google.com/store/apps/details?id=com.nextcloud.client&hl=de&gl=US), [FDroid](https://f-droid.org/de/packages/com.nextcloud.client/) |

To sync calender and contacts from your smartphone to Nextcloud DAVx⁵ needs to be installed and configured on the smartphone

- [Play Store](https://play.google.com/store/apps/details?id=at.bitfire.davdroid)
- [F-Droid](https://f-droid.org/de/packages/at.bitfire.davdroid/)

### Deck

`Deck` is a simple App to provide project management in Nextcloud.

| Platform | App                                                                                                                                                                                 |
| -------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Mobile   | [Play Store](https://play.google.com/store/apps/details?id=it.niedermann.nextcloud.deck.play&hl=de&gl=US), [F-Droid](https://f-droid.org/de/packages/it.niedermann.nextcloud.deck/) |

### Notes

Notes are stored in Nextcloud users root directory in markdown format and therefore can be used by any Markdown editor. Nextcloud Nodes comes with its own companion mobile app called `Nextcloud Notes`

| Platform | App                                                                                                                                                                            |
| -------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| Desktop  | [Obsidian](https://obsidian.md/)                                                                                                                                               |
| Mobile   | [Play Store](https://play.google.com/store/apps/details?id=it.niedermann.owncloud.notes&hl=de&gl=US), [F-Droid](https://f-droid.org/de/packages/it.niedermann.owncloud.notes/) |

### Cookbook

`Nextcloud Cookbook` stores recipes in user root directory in the folder `Rezepte`. There couple mobile apps you can pick from:

| App                | Store                                                                                                                                                                              |
| ------------------ | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Nextcloud Kochbuch | [Play Store](https://play.google.com/store/apps/details?id=de.lukasneugebauer.nextcloudcookbook), [F-Droid](https://f-droid.org/de/packages/de.lukasneugebauer.nextcloudcookbook/) |
| Nextcloud Cookbook | [F-Droid](https://f-droid.org/de/packages/de.micmun.android.nextcloudcookbook/)                                                                                                    |

### Bookmarks

Sync bookmarks between browsers and devices.

| Platform | App/Extension                                                                                                                                                |
| -------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| Chrome   | [Chrome Extension](https://chromewebstore.google.com/detail/floccus-bookmarks-sync/fnaicdffflnofjppbagibeoednhnbjhg)                                         |
| Firefox  | [Firefox Extension](https://addons.mozilla.org/en-US/firefox/addon/floccus/)                                                                                 |
| Mobile   | [Play Store](https://play.google.com/store/apps/details?id=org.handmadeideas.floccus), [F-Droid](https://f-droid.org/en/packages/org.handmadeideas.floccus/) |

> [List of all Extensions](https://floccus.org/download/)

### Social Login

### Onlyoffice

Call healthcheck manually

```shell
docker exec -u www-data nextcloud php occ onlyoffice:documentserver --check
```

Failing healthcheck:

- <https://help.nextcloud.com/t/onlyoffice-continuously-disconnects/169207/13>
- <https://github.com/ONLYOFFICE/onlyoffice-nextcloud/issues/876>

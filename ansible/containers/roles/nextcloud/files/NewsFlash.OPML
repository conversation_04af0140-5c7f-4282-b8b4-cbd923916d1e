<?xml version="1.0" encoding="UTF-8"?>
<opml version="2.0">
  <head>
    <title>NewsFlash OPML export</title>
  </head>
  <body>
    <outline title="Nachrichten" text="Nachrichten">
      <outline type="rss" text="tagesschau.de - die erste Adresse für Nachrichten und Information" htmlUrl="https://www.tagesschau.de/" xmlUrl="https://www.tagesschau.de/index~rss2.xml" title="tagesschau.de - die erste Adresse für Nachrichten und Information" />
      <outline xmlUrl="https://www.spiegel.de/schlagzeilen/index.rss" text="DER SPIEGEL - Schlagzeilen" type="rss" htmlUrl="https://www.spiegel.de/" title="DER SPIEGEL - Schlagzeilen" />
      <outline htmlUrl="https://www.handelsblatt.com/contentexport/feed/politik" xmlUrl="https://www.handelsblatt.com/contentexport/feed/politik" text="Handelsblatt Online - Politik" title="Handelsblatt Online - Politik" type="rss" />
      <outline htmlUrl="https://www.zdf.de/rss/zdf/nachrichten" xmlUrl="https://www.zdf.de/rss/zdf/nachrichten" title="ZDFheute" text="ZDFheute" type="rss" />
      <outline htmlUrl="http://www.dw.com/german/?maca=de-rss-de-all-1119-xml-mrss" text="Deutsche Welle: DW.com Deutsch" xmlUrl="https://rss.dw.com/xml/rss-de-all" title="Deutsche Welle: DW.com Deutsch" type="rss" />
      <outline xmlUrl="https://ec.europa.eu/eurostat/de/search?p_p_id=estatsearchportlet_WAR_estatsearchportlet&amp;p_p_lifecycle=2&amp;p_p_state=maximized&amp;p_p_mode=view&amp;p_p_resource_id=atom&amp;_estatsearchportlet_WAR_estatsearchportlet_collection=CAT_EURNEW" type="rss" htmlUrl="https://ec.europa.eu/eurostat" text="Eurostat - Custom RSS Feed" title="Eurostat - Custom RSS Feed" />
      <outline htmlUrl="https://mittelstandsschutz.de/magazin/feed/" title="Deutsche Mittelstandsschutz | Magazin" text="Deutsche Mittelstandsschutz | Magazin" type="rss" xmlUrl="https://mittelstandsschutz.de/magazin/feed/" />
      <outline type="rss" htmlUrl="https://www.stuttgarter-zeitung.de/schlagzeilen" text="Stuttgarter Zeitung - Schlagzeilen" xmlUrl="https://www.stuttgarter-zeitung.de/schlagzeilen.rss.feed" title="Stuttgarter Zeitung - Schlagzeilen" />
      <outline title="Wirtschaftswoche | Schlagzeilen" text="Wirtschaftswoche | Schlagzeilen" xmlUrl="https://www.wiwo.de/contentexport/feed/rss/schlagzeilen" type="rss" htmlUrl="https://www.wiwo.de/" />
      <outline title="Gesundheits-Check" type="rss" htmlUrl="https://scienceblogs.de/gesundheits-check/feed/" text="Gesundheits-Check" xmlUrl="http://scienceblogs.de/gesundheits-check/feed/" />
      <outline htmlUrl="https://www.reuters.com/world" type="rss" title="World News | Reuters.com" text="World News | Reuters.com" xmlUrl="https://cdn.feedcontrol.net/8/1115-TvWAhu4G064WT.xml" />
      <outline htmlUrl="https://www.wiwo.de/" title="Wirtschaftswoche  | Unternehmen" xmlUrl="https://www.wiwo.de/contentexport/feed/rss/unternehmen" type="rss" text="Wirtschaftswoche  | Unternehmen" />
      <outline xmlUrl="https://cdn.feedcontrol.net/8/1114-wioSIX3uu8MEj.xml" type="rss" htmlUrl="https://www.reuters.com/" text="Reuters home page" title="Reuters home page" />
      <outline htmlUrl="https://feeds.t-online.de/rss/nachrichten" text="nachrichten | t-online" type="rss" xmlUrl="http://feeds.t-online.de/rss/nachrichten" title="nachrichten | t-online" />
      <outline type="rss" text="News - Alle News, Analysen, Bilder &amp; Videos | Blick.ch" xmlUrl="http://www.blick.ch/news/rss.xml" htmlUrl="https://www.blick.ch/" title="News - Alle News, Analysen, Bilder &amp; Videos | Blick.ch" />
      <outline htmlUrl="https://ec.europa.eu/eurostat" text="Eurostat - Custom RSS Feed" type="rss" xmlUrl="https://ec.europa.eu/eurostat/de/search?p_p_id=estatsearchportlet_WAR_estatsearchportlet&amp;p_p_lifecycle=2&amp;p_p_state=maximized&amp;p_p_mode=view&amp;p_p_resource_id=atom&amp;_estatsearchportlet_WAR_estatsearchportlet_collection=CAT_PREREL" title="Eurostat - Custom RSS Feed" />
      <outline xmlUrl="https://newsfeed.zeit.de/news/index" text="Aktuelle Schlagzeilen und Newsticker" type="rss" htmlUrl="https://www.zeit.de/news/index" title="Aktuelle Schlagzeilen und Newsticker" />
      <outline type="rss" text="Aktuell - FAZ.NET" htmlUrl="https://www.faz.net/aktuell/" title="Aktuell - FAZ.NET" xmlUrl="https://www.faz.net/rss/aktuell" />
      <outline text="News, Politics, Opinion, Commentary, and Analysis" type="rss" htmlUrl="https://www.newyorker.com/" xmlUrl="http://www.newyorker.com/feed/news" title="News, Politics, Opinion, Commentary, and Analysis" />
      <outline htmlUrl="https://www.reuters.com/investigates/" xmlUrl="https://cdn.feedcontrol.net/8/1117-sj0Xoer9nTe0b.xml" title="Special Reports from Reuters journalists around the world" text="Special Reports from Reuters journalists around the world" type="rss" />
      <outline text="Medizin – ScienceBlogs auf Deutsch" type="rss" title="Medizin – ScienceBlogs auf Deutsch" xmlUrl="http://scienceblogs.de/channel/medizin/feed/" htmlUrl="https://scienceblogs.de/channel/medizin/feed/" />
      <outline title="Reuters News Agency" type="rss" xmlUrl="https://www.reutersagency.com/feed/?taxonomy=best-sectors&amp;post_type=best" text="Reuters News Agency" htmlUrl="https://www.reutersagency.com/feed/?taxonomy=best-sectors&amp;post_type=best" />
    </outline>
    <outline text="Wissenschaft" title="Wissenschaft">
      <outline htmlUrl="https://www.science.org/journal/science" title="Science" xmlUrl="http://www.sciencemag.org/rss/current.xml" type="rss" text="Science" />
      <outline xmlUrl="http://feeds.feedburner.com/smithsonianmag/science-nature" htmlUrl="https://www.smithsonianmag.com/rss/science-nature/" text="Science | smithsonianmag.com" title="Science | smithsonianmag.com" type="rss" />
      <outline title="ScienceDirect Publication: Cell" xmlUrl="http://rss.sciencedirect.com/publication/science/00928674" htmlUrl="https://www.sciencedirect.com/journal/cell" type="rss" text="ScienceDirect Publication: Cell" />
      <outline xmlUrl="http://feeds.arstechnica.com/arstechnica/science" htmlUrl="https://arstechnica.com/science/feed/" text="Science – Ars Technica" type="rss" title="Science – Ars Technica" />
      <outline xmlUrl="https://www.weltderphysik.de/intern/index.php?PID=36" type="rss" title="Welt der Physik" text="Welt der Physik" htmlUrl="https://www.weltderphysik.de/RSS-alles" />
      <outline text="Welt der Physik" htmlUrl="https://www.weltderphysik.de/RSS-alles" xmlUrl="https://www.weltderphysik.de/intern/index.php?PID=35" title="Welt der Physik" type="rss" />
      <outline type="rss" text="Renewable Energy News -- ScienceDaily" htmlUrl="https://www.sciencedaily.com/news/earth_climate/renewable_energy/" xmlUrl="http://www.sciencedaily.com/rss/earth_climate/renewable_energy.xml" title="Renewable Energy News -- ScienceDaily" />
      <outline htmlUrl="https://www.science.org/" title="Latest News from Science Magazine" type="rss" text="Latest News from Science Magazine" xmlUrl="http://www.sciencemag.org/rss/news_current.xml" />
      <outline text="Science | The Guardian" xmlUrl="http://feeds.guardian.co.uk/theguardian/science/rss" type="rss" htmlUrl="https://www.theguardian.com/science" title="Science | The Guardian" />
      <outline xmlUrl="http://feeds.feedburner.com/ScienceBlogs/Global" type="rss" htmlUrl="http://www.scienceblogs.de/" title="ScienceBlogs" text="ScienceBlogs" />
      <outline text="Science-Based Medicine" xmlUrl="http://www.sciencebasedmedicine.org/?feed=rss2" htmlUrl="https://sciencebasedmedicine.org/feed/" type="rss" title="Science-Based Medicine" />
      <outline xmlUrl="http://www.sciencedaily.com/rss/mind_brain/psychology.xml" htmlUrl="https://www.sciencedaily.com/news/mind_brain/psychology/" type="rss" title="Psychology Research News -- ScienceDaily" text="Psychology Research News -- ScienceDaily" />
      <outline text="Health &amp; Medicine News -- ScienceDaily" type="rss" htmlUrl="https://www.sciencedaily.com/news/health_medicine/" xmlUrl="http://www.sciencedaily.com/rss/health_medicine.xml" title="Health &amp; Medicine News -- ScienceDaily" />
      <outline type="rss" text="Neuroscience News -- ScienceDaily" htmlUrl="https://www.sciencedaily.com/news/mind_brain/neuroscience/" title="Neuroscience News -- ScienceDaily" xmlUrl="http://www.sciencedaily.com/rss/mind_brain/neuroscience.xml" />
      <outline text="Mind &amp; Brain News -- ScienceDaily" type="rss" xmlUrl="http://www.sciencedaily.com/rss/mind_brain.xml" title="Mind &amp; Brain News -- ScienceDaily" htmlUrl="https://www.sciencedaily.com/news/mind_brain/" />
      <outline xmlUrl="http://feeds.feedburner.com/sciencealert-latestnews" type="rss" text="ScienceAlert" htmlUrl="https://www.sciencealert.com/feed" title="ScienceAlert" />
      <outline htmlUrl="https://www.sciencedaily.com/" title="Latest Science News -- ScienceDaily" text="Latest Science News -- ScienceDaily" type="rss" xmlUrl="http://www.sciencedaily.com/newsfeed.xml" />
      <outline htmlUrl="https://www.weltderphysik.de/RSS-Forschung" xmlUrl="https://www.weltderphysik.de/intern/index.php?PID=34" text="Welt der Physik | Aktuelles" type="rss" title="Welt der Physik | Aktuelles" />
      <outline type="rss" title="Science &amp; technology" xmlUrl="http://www.economist.com/rss/science_and_technology_rss.xml" htmlUrl="https://www.economist.com/science-and-technology/" text="Science &amp; technology" />
      <outline htmlUrl="https://www.sciencedaily.com/news/computers_math/artificial_intelligence/" type="rss" title="Artificial Intelligence News -- ScienceDaily" xmlUrl="http://www.sciencedaily.com/rss/computers_math/artificial_intelligence.xml" text="Artificial Intelligence News -- ScienceDaily" />
      <outline htmlUrl="https://www.wired.com/" type="rss" title="Science Latest" text="Science Latest" xmlUrl="http://blog.wired.com/wiredscience/rss.xml" />
      <outline text="scinexx | Das Wissensmagazin" type="rss" xmlUrl="https://feeds.feedburner.com/scinexx" htmlUrl="https://www.scinexx.de/" title="scinexx | Das Wissensmagazin" />
    </outline>
    <outline text="Technik" title="Technik">
      <outline xmlUrl="http://intelmsl.com/blog/feed/" text="OSINT Blog - Open Source Intelligence Tips &amp; Insights - IMSL" title="OSINT Blog - Open Source Intelligence Tips &amp; Insights - IMSL" htmlUrl="https://www.intelmsl.com/blog/feed/" type="rss" />
      <outline title="Wirtschaftswoche | Technologie" type="rss" text="Wirtschaftswoche | Technologie" htmlUrl="https://www.wiwo.de/" xmlUrl="https://www.wiwo.de/contentexport/feed/rss/technologie" />
      <outline title="IT Business News – IT BOLTWISE® x Artificial Intelligence" xmlUrl="https://www.it-boltwise.de/themen/allgemein/feed" text="IT Business News – IT BOLTWISE® x Artificial Intelligence" type="rss" htmlUrl="https://www.it-boltwise.de/themen/allgemein/feed" />
      <outline title="Technik &amp; Motor - FAZ.NET" text="Technik &amp; Motor - FAZ.NET" xmlUrl="http://www.faz.net/aktuell/technik-motor/?rssview=1" htmlUrl="https://www.faz.net/aktuell/technik-motor/" type="rss" />
      <outline xmlUrl="http://www.brianlinkletter.com/feed/" htmlUrl="https://brianlinkletter.com/feed/" type="rss" text="Open-Source Routing and Network Simulation" title="Open-Source Routing and Network Simulation" />
      <outline type="rss" htmlUrl="https://seclists.org/#oss-sec" title="Open Source Security" text="Open Source Security" xmlUrl="http://seclists.org/rss/oss-sec.rss" />
      <outline text="Technology sector" type="rss" htmlUrl="https://www.ft.com/stream/6b32f2c1-da43-4e19-80b9-8aef4ab640d7" title="Technology sector" xmlUrl="http://www.ft.com/rss/companies/technology" />
      <outline xmlUrl="https://www.reddit.com/r/OSINT/.rss" title="Open Source Intelligence" htmlUrl="https://www.reddit.com/r/OSINT/" type="rss" text="Open Source Intelligence" />
      <outline type="rss" text="DataCenter-Insider | News | RSS-Feed" htmlUrl="https://www.datacenter-insider.de/" title="DataCenter-Insider | News | RSS-Feed" xmlUrl="https://www.datacenter-insider.de/rss/news.xml" />
      <outline type="rss" text="techtag" htmlUrl="https://www.techtag.de/feed/" title="techtag" xmlUrl="http://www.techtag.de/feed/" />
      <outline xmlUrl="http://feeds.feedburner.com/OpenSourceHacker" text="Open Source Hacker" htmlUrl="https://opensourcehacker.com/" title="Open Source Hacker" type="rss" />
      <outline type="rss" text="Smart Data Center" xmlUrl="https://www.smart-datacenter.de/magazin/feed/" htmlUrl="https://www.smart-datacenter.de/magazin/feed/" title="Smart Data Center" />
      <outline text="Open Source on Reddit" xmlUrl="http://www.reddit.com/r/opensource/.rss" title="Open Source on Reddit" type="rss" htmlUrl="https://www.reddit.com/r/opensource/" />
      <outline text="CloudComputing-Insider | News | RSS-Feed" title="CloudComputing-Insider | News | RSS-Feed" htmlUrl="https://www.cloudcomputing-insider.de/" type="rss" xmlUrl="https://www.cloudcomputing-insider.de/rss/news.xml" />
      <outline type="rss" title="Technology | The Guardian" xmlUrl="http://www.theguardian.com/technology/rss" htmlUrl="https://www.theguardian.com/uk/technology" text="Technology | The Guardian" />
      <outline type="rss" xmlUrl="https://openssf.org/feed/" title="Open Source Security Foundation" text="Open Source Security Foundation" htmlUrl="https://openssf.org/feed/" />
      <outline title="Security-Insider | News | RSS-Feed" xmlUrl="https://www.security-insider.de/rss/news.xml" type="rss" htmlUrl="https://www.security-insider.de/" text="Security-Insider | News | RSS-Feed" />
      <outline xmlUrl="http://osintdaily.blogspot.com/feeds/posts/default" htmlUrl="http://osintdaily.blogspot.com/" text="OPEN SOURCE INTELLIGENCE (OSINT) NEWS" title="OPEN SOURCE INTELLIGENCE (OSINT) NEWS" type="rss" />
      <outline title="Microsoft Open Source Blog" type="rss" htmlUrl="https://cloudblogs.microsoft.com/opensource/feed/" text="Microsoft Open Source Blog" xmlUrl="http://open.microsoft.com/feed/" />
      <outline text="AWS Open Source Blog" htmlUrl="https://aws.amazon.com/blogs/opensource/feed/" title="AWS Open Source Blog" xmlUrl="https://aws.amazon.com/blogs/opensource/feed/" type="rss" />
      <outline text="Golem.de - Open Source Software" type="rss" htmlUrl="https://www.golem.de/" title="Golem.de - Open Source Software" xmlUrl="http://rss.golem.de/rss.php?tp=oss&amp;feed=RSS2.0" />
      <outline xmlUrl="https://news.ycombinator.com/rss" text="Hacker News" htmlUrl="https://news.ycombinator.com/" title="Hacker News" type="rss" />
      <outline type="rss" text="Trends der Zukunft" xmlUrl="https://feeds.feedburner.com/TrendsDerZukunft" htmlUrl="https://www.trendsderzukunft.de/feed/" title="Trends der Zukunft" />
      <outline text="heise online News" type="rss" xmlUrl="https://www.heise.de/rss/heise-atom.xml" htmlUrl="https://www.heise.de/" title="heise online News" />
      <outline type="rss" xmlUrl="https://rss.golem.de/rss.php?feed=RSS2.0" title="Golem.de" text="Golem.de" htmlUrl="https://www.golem.de/" />
      <outline xmlUrl="http://wissenschaft.pr-gateway.de/feed/" htmlUrl="https://wissenschaft.pr-gateway.de/feed/" text="Technik Wissenschaft Forschung" type="rss" title="Technik Wissenschaft Forschung" />
      <outline title="TechSpot" text="TechSpot" xmlUrl="http://www.techspot.com/backend.xml" type="rss" htmlUrl="https://www.techspot.com/" />
    </outline>
    <outline text="Youtube" title="Youtube">
      <outline text="Kurzgesagt – In a Nutshell" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCsXVk37bltHxD1rDPwtNM8Q" type="rss" htmlUrl="https://www.youtube.com/channel/UCsXVk37bltHxD1rDPwtNM8Q" title="Kurzgesagt – In a Nutshell" />
      <outline type="rss" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UC6biysICWOJ-C3P4Tyeggzg" htmlUrl="https://www.youtube.com/channel/UC6biysICWOJ-C3P4Tyeggzg" title="Low Level Learning" text="Low Level Learning" />
      <outline type="rss" htmlUrl="https://www.youtube.com/channel/UC7_gcs09iThXybpVgjHZ_7g" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UC7_gcs09iThXybpVgjHZ_7g" text="PBS Space Time" title="PBS Space Time" />
      <outline text="Joe Blogs" type="rss" htmlUrl="https://www.youtube.com/channel/UCjniKviAJH0mENoLStpQXmQ" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCjniKviAJH0mENoLStpQXmQ" title="Joe Blogs" />
      <outline title="DW News" text="DW News" type="rss" htmlUrl="https://www.youtube.com/channel/UCknLrEdhRCp1aegoMqRaCZg" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCknLrEdhRCp1aegoMqRaCZg" />
      <outline text="Veritasium" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCHnyfMqiRRG1u-2MsSQLbXA" htmlUrl="https://www.youtube.com/channel/UCHnyfMqiRRG1u-2MsSQLbXA" title="Veritasium" type="rss" />
      <outline type="rss" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCfe_znKY1ukrqlGActlFmaQ" htmlUrl="https://www.youtube.com/channel/UCfe_znKY1ukrqlGActlFmaQ" title="Healthy Software Developer" text="Healthy Software Developer" />
      <outline text="Crecganford" htmlUrl="https://www.youtube.com/channel/UChhMB_J0kz8eBJECy4d5uSQ" title="Crecganford" type="rss" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UChhMB_J0kz8eBJECy4d5uSQ" />
      <outline title="Simplicissimus" type="rss" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCKGMHVipEvuZudhHD05FOYA" htmlUrl="https://www.youtube.com/channel/UCKGMHVipEvuZudhHD05FOYA" text="Simplicissimus" />
      <outline xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCVeW9qkBjo3zosnqUbG7CFw" htmlUrl="https://www.youtube.com/channel/UCVeW9qkBjo3zosnqUbG7CFw" text="John Hammond" type="rss" title="John Hammond" />
      <outline htmlUrl="https://www.youtube.com/channel/UCY1kMZp36IQSyNx_9h4mpCg" title="Mark Rober" text="Mark Rober" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCY1kMZp36IQSyNx_9h4mpCg" type="rss" />
      <outline htmlUrl="https://www.youtube.com/channel/UCnZeeKO8qVdtkrA4-uKhtTw" title="Animagraffs" text="Animagraffs" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCnZeeKO8qVdtkrA4-uKhtTw" type="rss" />
      <outline text="Tobias Huch" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCBA2L-XIgGYVn_t_8B0VMMg" htmlUrl="https://www.youtube.com/channel/UCBA2L-XIgGYVn_t_8B0VMMg" type="rss" title="Tobias Huch" />
      <outline htmlUrl="https://www.youtube.com/channel/UCSUn0G38wUfKzgKRVcP8yRQ" title="Offys Werkstatt" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCSUn0G38wUfKzgKRVcP8yRQ" type="rss" text="Offys Werkstatt" />
      <outline type="rss" text="frontal" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCDw0K7K658-ztRuCn2mn0Kw" htmlUrl="https://www.youtube.com/channel/UCDw0K7K658-ztRuCn2mn0Kw" title="frontal" />
      <outline xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCgBg0aacyJnw4qUnb1FlfEQ" title="Institute of Human Anatomy" type="rss" text="Institute of Human Anatomy" htmlUrl="https://www.youtube.com/channel/UCgBg0aacyJnw4qUnb1FlfEQ" />
      <outline title="NetworkChuck" type="rss" htmlUrl="https://www.youtube.com/channel/UC9x0AN7BWHpCDHSm9NiJFJQ" text="NetworkChuck" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UC9x0AN7BWHpCDHSm9NiJFJQ" />
      <outline xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCLLibJTCy3sXjHLVaDimnpQ" type="rss" htmlUrl="https://www.youtube.com/channel/UCLLibJTCy3sXjHLVaDimnpQ" title="ARTEde" text="ARTEde" />
      <outline text="Militär &amp; Geschichte mit Torsten Heinrich" title="Militär &amp; Geschichte mit Torsten Heinrich" htmlUrl="https://www.youtube.com/channel/UC9kZ6FlOQfusBV8LS2x2fAA" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UC9kZ6FlOQfusBV8LS2x2fAA" type="rss" />
      <outline text="NileRed" title="NileRed" type="rss" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCFhXFikryT4aFcLkLw2LBLA" htmlUrl="https://www.youtube.com/channel/UCFhXFikryT4aFcLkLw2LBLA" />
      <outline text="DW Deutsch" htmlUrl="https://www.youtube.com/channel/UCMIgOXM2JEQ2Pv2d0_PVfcg" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCMIgOXM2JEQ2Pv2d0_PVfcg" type="rss" title="DW Deutsch" />
      <outline text="Biblaridion" type="rss" htmlUrl="https://www.youtube.com/channel/UCMjTcpv56G_W0FRIdPHBn4A" title="Biblaridion" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCMjTcpv56G_W0FRIdPHBn4A" />
      <outline text="tagesschau" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UC5NOEUbkLheQcaaRldYW5GA" title="tagesschau" htmlUrl="https://www.youtube.com/channel/UC5NOEUbkLheQcaaRldYW5GA" type="rss" />
      <outline text="ZDF heute-show" type="rss" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCFqcNI0NaAA21NS9W3ExCRg" htmlUrl="https://www.youtube.com/channel/UCFqcNI0NaAA21NS9W3ExCRg" title="ZDF heute-show" />
      <outline text="ZDFheute Nachrichten" type="rss" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCeqKIgPQfNInOswGRWt48kQ" title="ZDFheute Nachrichten" htmlUrl="https://www.youtube.com/channel/UCeqKIgPQfNInOswGRWt48kQ" />
      <outline type="rss" text="Sabine Hossenfelder" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UC1yNl2E66ZzKApQdRuTQ4tw" htmlUrl="https://www.youtube.com/channel/UC1yNl2E66ZzKApQdRuTQ4tw" title="Sabine Hossenfelder" />
      <outline text="VisualPolitik DE" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCHmNfmKhIkfBMTrr5QU-gAQ" title="VisualPolitik DE" type="rss" htmlUrl="https://www.youtube.com/channel/UCHmNfmKhIkfBMTrr5QU-gAQ" />
      <outline text="Mark Reicher" htmlUrl="https://www.youtube.com/channel/UCcJ468cakFPyFEcjrFGz0Og" title="Mark Reicher" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCcJ468cakFPyFEcjrFGz0Og" type="rss" />
      <outline text="DIY Perks" type="rss" htmlUrl="https://www.youtube.com/channel/UCUQo7nzH1sXVpzL92VesANw" title="DIY Perks" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCUQo7nzH1sXVpzL92VesANw" />
      <outline htmlUrl="https://www.youtube.com/channel/UC6mIxFTvXkWQVEHPsEdflzQ" type="rss" text="GreatScott!" title="GreatScott!" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UC6mIxFTvXkWQVEHPsEdflzQ" />
      <outline text="Ben Eater" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCS0N5baNlQWJCUrhCEo8WlA" type="rss" htmlUrl="https://www.youtube.com/channel/UCS0N5baNlQWJCUrhCEo8WlA" title="Ben Eater" />
      <outline title="Anton Petrov" text="Anton Petrov" htmlUrl="https://www.youtube.com/channel/UCciQ8wFcVoIIMi-lfu8-cjQ" type="rss" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCciQ8wFcVoIIMi-lfu8-cjQ" />
      <outline text="ElectroBOOM" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCJ0-OtVpF0wOKEqT2Z1HEtA" type="rss" htmlUrl="https://www.youtube.com/channel/UCJ0-OtVpF0wOKEqT2Z1HEtA" title="ElectroBOOM" />
      <outline xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCu7_D0o48KbfhpEohoP7YSQ" title="Andreas Spiess" text="Andreas Spiess" type="rss" htmlUrl="https://www.youtube.com/channel/UCu7_D0o48KbfhpEohoP7YSQ" />
      <outline title="Christian Lempa" text="Christian Lempa" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCZNhwA1B5YqiY1nLzmM0ZRg" htmlUrl="https://www.youtube.com/channel/UCZNhwA1B5YqiY1nLzmM0ZRg" type="rss" />
      <outline text="Konstantin Flemig" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UChiFVh8U2d6cyJIh0ghNF3w" htmlUrl="https://www.youtube.com/channel/UChiFVh8U2d6cyJIh0ghNF3w" title="Konstantin Flemig" type="rss" />
      <outline htmlUrl="https://www.youtube.com/channel/UCNNEMxGKV1LsKZRt4vaIbvw" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCNNEMxGKV1LsKZRt4vaIbvw" type="rss" title="ZDF MAGAZIN ROYALE" text="ZDF MAGAZIN ROYALE" />
      <outline text="VisualEconomik DE" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCL5ysObh1s_qTYq_37-KffQ" htmlUrl="https://www.youtube.com/channel/UCL5ysObh1s_qTYq_37-KffQ" title="VisualEconomik DE" type="rss" />
      <outline title="David Bombal" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCP7WmQ_U4GB3K51Od9QvM0w" text="David Bombal" htmlUrl="https://www.youtube.com/channel/UCP7WmQ_U4GB3K51Od9QvM0w" type="rss" />
      <outline text="NEVER TOO SMALL" type="rss" title="NEVER TOO SMALL" htmlUrl="https://www.youtube.com/channel/UC_zQ777U6YTyatP3P1wi3xw" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UC_zQ777U6YTyatP3P1wi3xw" />
      <outline text="The Cherno" type="rss" htmlUrl="https://www.youtube.com/channel/UCQ-W1KE9EYfdxhL6S4twUNw" title="The Cherno" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCQ-W1KE9EYfdxhL6S4twUNw" />
      <outline type="rss" htmlUrl="https://www.youtube.com/channel/UCzR-rom72PHN9Zg7RML9EbA" text="PBS Eons" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCzR-rom72PHN9Zg7RML9EbA" title="PBS Eons" />
      <outline text="City Prepping" type="rss" xmlUrl="https://www.youtube.com/feeds/videos.xml?channel_id=UCmb2QRAjdnkse21CtxAQ-cA" title="City Prepping" htmlUrl="https://www.youtube.com/channel/UCmb2QRAjdnkse21CtxAQ-cA" />
    </outline>
  </body>
</opml>
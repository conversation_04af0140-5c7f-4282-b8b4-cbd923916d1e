# code: language=ansible
---
- name: create docker volume to store onlyoffice data
  docker_volume:
    name: "{{ item }}"
    state: present
  loop:
    - nextcloud-onlyoffice-volume
    - nextcloud-onlyoffice-postgres-volume
    - nextcloud-onlyoffice-redis-volume
    - nextcloud-onlyoffice-rabbitmq-volume
    - nextcloud-onlyoffice-lib-volume
    - nextcloud-onlyoffice-log-volume
    - nextcloud-onlyoffice-fonts-volume

- name: create docker container for onlyoffice
  register: nextcloud_onlyoffice
  docker_container:
    name: nextcloud-onlyoffice
    image: "onlyoffice/documentserver:{{ nextcloud_onlyoffice_version }}"
    state: started
    restart_policy: unless-stopped
    volumes:
      - nextcloud-onlyoffice-volume:/var/www/onlyoffice/Data
      - nextcloud-onlyoffice-postgres-volume:/var/lib/postgresql
      - nextcloud-onlyoffice-redis-volume:/var/lib/redis
      - nextcloud-onlyoffice-rabbitmq-volume:/var/lib/rabbitmq
      - nextcloud-onlyoffice-lib-volume:/var/lib/onlyoffice
      - nextcloud-onlyoffice-log-volume:/var/log/onlyoffice
      - nextcloud-onlyoffice-fonts-volume:/usr/share/fonts/truetype/custom
    networks:
      - name: "{{ traefik_proxy_network_name | mandatory }}"
    env:
      JWT_SECRET: "{{ nextcloud_onlyoffice_secret }}"

    labels:
      traefik.enable: "true"

      traefik.http.routers.onlyoffice.entrypoints: web
      traefik.http.routers.onlyoffice.rule: "Host(`{{ nextcloud_onlyoffice_url }}`)"
      traefik.http.routers.onlyoffice.middlewares: onlyoffice-https

      traefik.http.routers.onlyoffice-https.tls: "true"
      traefik.http.routers.onlyoffice-https.tls.certresolver: letsencrypt
      traefik.http.routers.onlyoffice-https.entrypoints: web-secured
      traefik.http.routers.onlyoffice-https.rule: "Host(`{{ nextcloud_onlyoffice_url }}`)"
      traefik.http.middlewares.onlyoffice-https.redirectscheme.scheme: https
      traefik.http.services.onlyoffice-https.loadbalancer.server.port: "80"
      traefik.http.services.onlyoffice-https.loadbalancer.passhostheader: "true"

      traefik.http.routers.onlyoffice-https.middlewares: onlyoffice-headers
      traefik.http.middlewares.onlyoffice-headers.headers.customRequestHeaders.X-Forwarded-Proto: https
      traefik.http.middlewares.onlyoffice-headers.headers.stsSeconds: "31536002"
      traefik.http.middlewares.onlyoffice-headers.headers.customFrameOptionsValue: "ALLOW-FROM https://{{ nextcloud_url | mandatory }}"
      traefik.http.middlewares.onlyoffice-headers.headers.contentSecurityPolicy: "frame-ancestors 'self' {{ nextcloud_url | mandatory }} *.{{ nextcloud_url | mandatory }}"

- name: configure onlyoffice after installation
  when: nextcloud_onlyoffice.changed
  block:
    - name: install onlyoffice nextcloud app
      community.docker.docker_container_exec:
        container: nextcloud
        user: www-data
        argv:
          - php
          - occ
          - app:install
          - onlyoffice
      register: onlyoffice_app_installed
      failed_when: onlyoffice_app_installed.rc == 2

    - name: configure onlyoffice nextcloud app
      when: onlyoffice_app_installed.changed and onlyoffice_app_installed.rc > 0
      block:
        - name: add onlyoffice to trusted domains
          community.docker.docker_container_exec:
            container: nextcloud
            user: www-data
            argv:
              - php
              - occ
              - config:system:set
              - trusted_domains
              - 2
              - "--value=https://{{ nextcloud_onlyoffice_url }}"

        - name: configure onlyoffice url
          community.docker.docker_container_exec:
            container: nextcloud
            user: www-data
            argv:
              - php
              - occ
              - config:app:set
              - onlyoffice
              - DocumentServerUrl
              - "--value=https://{{ nextcloud_onlyoffice_url }}"

        - name: disable certification check in onlyoffice app
          community.docker.docker_container_exec:
            container: nextcloud
            user: www-data
            argv:
              - php
              - occ
              - config:app:set
              - onlyoffice
              - verify_peer_off
              - --value=true

        - name: set onlyoffice jwt secret
          community.docker.docker_container_exec:
            container: nextcloud
            user: www-data
            argv:
              - php
              - occ
              - config:app:set
              - onlyoffice
              - jwt_secret
              - "--value={{ nextcloud_onlyoffice_secret }}"

        - name: set nextcloud storage url
          community.docker.docker_container_exec:
            container: nextcloud
            user: www-data
            argv:
              - php
              - occ
              - config:app:set
              - onlyoffice
              - StorageUrl
              - "--value=https://{{ nextcloud_url }}"

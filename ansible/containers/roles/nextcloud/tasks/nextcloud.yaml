# code: language=ansible
#
# Run nextcloud in a docker container
# Requires
#   - nextcloud_network_name
---
- name: create docker volume for nextcloud
  docker_volume:
    name: nextcloud-volume
    state: present

- name: find ip of reverse proxy
  shell:
    cmd: sh -c "docker inspect -f '{{ '{{' }}json (index .IPAM.Config 0).Subnet{{ '}}' }}' {{ traefik_proxy_network_name }} | sed 's/\"//g'"
  register: reverse_proxy_ip
  changed_when: false

- name: create docker container for nextcloud
  register: nextcloud
  docker_container:
    name: nextcloud
    image: "nextcloud:{{ nextcloud_version }}"
    state: started
    restart_policy: unless-stopped
    networks:
      - name: "{{ traefik_proxy_network_name | mandatory }}"
      - name: "{{ nextcloud_network_name | mandatory }}"
        links:
          - "nextcloud-redis:redis"
          - "nextcloud-mariadb:mariadb"
    volumes:
      - nextcloud-volume:/var/www/html
    env:
      NEXTCLOUD_ADMIN_USER: "{{ nextcloud_admin_username }}"
      NEXTCLOUD_ADMIN_PASSWORD: "{{ nextcloud_admin_password }}"
      NEXTCLOUD_TRUSTED_DOMAINS: "{{ nextcloud_url }}"
      REDIS_HOST: redis
      REDIS_HOST_PORT: "6379"
      REDIS_HOST_PASSWORD: "{{ nextcloud_redis_secret }}"
      MYSQL_HOST: mariadb
      MYSQL_PORT: "3306"
      MYSQL_DATABASE: "{{ nextcloud_mariadb_database }}"
      MYSQL_USER: "{{ nextcloud_mariadb_user }}"
      MYSQL_PASSWORD: "{{ nextcloud_mariadb_password }}"
      TRUSTED_PROXIES: "{{ reverse_proxy_ip.stdout }}"
      OVERWRITEPROTOCOL: https
      OVERWRITECLIURL: "https://{{ nextcloud_url }}"
      SMTP_HOST: "{{ smtp_host }}"
      SMTP_PORT: "{{ smtp_port | string }}"
      SMTP_SECURE: "{%- if smtp_encryption == 'starttls' -%}tls{%- endif -%}"
      MAIL_FROM_ADDRESS: "{{ smtp_username | split('@') | first }}"
      MAIL_DOMAIN: "{{ smtp_username | split('@') | last }}"
      SMTP_NAME: "{{smtp_username  }}"
      SMTP_PASSWORD: "{{ smtp_password }}"

    labels:
      backup.enable: "true"
      traefik.enable: "true"

      traefik.http.routers.nextcloud.entrypoints: web
      traefik.http.routers.nextcloud.rule: "Host(`{{ nextcloud_url }}`)"
      traefik.http.routers.nextcloud.middlewares: nextcloud-https

      traefik.http.routers.nextcloud-https.tls: "true"
      traefik.http.routers.nextcloud-https.tls.certresolver: letsencrypt
      traefik.http.routers.nextcloud-https.entrypoints: web-secured
      traefik.http.routers.nextcloud-https.rule: "Host(`{{ nextcloud_url }}`)"
      traefik.http.middlewares.nextcloud-https.redirectscheme.scheme: https
      traefik.http.services.nextcloud-https.loadbalancer.server.port: "80"

      traefik.http.routers.nextcloud-https.middlewares: nextcloud-dav,nextcloud-headers
      traefik.http.middlewares.nextcloud-headers.headers.customRequestHeaders.X-Forwarded-Proto: "https"
      traefik.http.middlewares.nextcloud-headers.headers.stsSeconds: "31536000"
      traefik.http.middlewares.nextcloud-headers.headers.customFrameOptionsValue: "SAMEORIGIN"

      traefik.http.middlewares.nextcloud-dav.redirectregex.permanent: "true"
      traefik.http.middlewares.nextcloud-dav.redirectregex.regex: "^http(s?)://(.*)/.well-known/(?:card|cal)dav"
      traefik.http.middlewares.nextcloud-dav.redirectregex.replacement: "https://{{ nextcloud_url }}/remote.php/dav"

- name: pause until nextcloud setup is complete
  when: nextcloud.changed
  pause:
    minutes: 1

- name: set default phone region
  when: nextcloud.changed
  community.docker.docker_container_exec:
    container: nextcloud
    user: www-data
    argv:
      - php
      - occ
      - config:system:set
      - default_phone_region
      - --value=de

- name: use cron jobs for nextcloud updates
  when: nextcloud_use_cron_job and nextcloud.changed
  tags:
    - nextcloud-cron
  block:
    - name: create nextloud config directory
      become: true
      file:
        state: directory
        path: "{{ nextcloud_config_directory }}"
        owner: "{{ ansible_user_id }}"
        group: "{{ ansible_user_id }}"

    - name: copy cron job into the nextcloud config directory
      copy:
        src: nextcloud-cron-job.sh
        dest: "{{ nextcloud_config_directory }}"
        mode: 0755

    - name: create cron job for nextcloud
      cron:
        name: "general nextcloud cron"
        minute: "*/5"
        job: "{{ nextcloud_config_directory }}/nextcloud-cron-job.sh"
      register: cron_created

    - name: enable cron in nextcloud
      when: cron_created.changed
      community.docker.docker_container_exec:
        container: nextcloud
        user: www-data
        argv:
          - php
          - occ
          - background:cron

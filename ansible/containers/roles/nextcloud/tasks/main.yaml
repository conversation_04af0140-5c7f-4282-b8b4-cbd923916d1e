# code: language=ansible
---
- name: create docker network for nextcloud
  docker_network:
    name: "{{ nextcloud_network_name }}"
    state: present
  tags:
    - nextcloud

- import_tasks: redis.yaml
  tags:
    - nextcloud
    - nextcloud-redis
    - redis

- import_tasks: mariadb.yaml
  tags:
    - nextcloud
    - nextcloud-mariadb
    - nextcloud-mysql
    - mysql
    - mariadb

- import_tasks: nextcloud.yaml
  tags:
    - nextcloud

- import_tasks: nextcloud-apps.yaml
  tags:
    - nextcloud
    - nextcloud-apps

- import_tasks: onlyoffice.yaml
  when: nextcloud_use_onlyoffice
  tags:
    - nextcloud
    - nextcloud-onlyoffice
    - onlyoffice

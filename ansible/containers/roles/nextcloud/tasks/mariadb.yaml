# code: language=ansible
#
# Run mariadb in a docker container for nextcloud
# Requires:
#   - nextcloud_network_name
---
- name: create docker volume for mariadb
  docker_volume:
    name: nextcloud-mariadb-volume
    state: present

- name: create mariadb in a docker container
  docker_container:
    name: nextcloud-mariadb
    image: "mariadb:{{ nextcloud_mariadb_version }}"
    state: started
    restart_policy: unless-stopped
    command: --transaction-isolation=READ-COMMITTED --binlog-format=ROW
    networks:
      - name: "{{ nextcloud_network_name }}"
    volumes:
      - nextcloud-mariadb-volume:/var/lib/mysql
    env:
      MARIADB_ROOT_PASSWORD: "{{ nextcloud_mariadb_root_password }}"
      MARIADB_DATABASE: "{{ nextcloud_mariadb_database }}"
      MARIADB_USER: "{{ nextcloud_mariadb_user }}"
      MARIADB_PASSWORD: "{{ nextcloud_mariadb_password }}"
    labels:
      backup.enable: "true"
      backup.mysql: "true"

# - name: make backup with mysqldump
#   community.docker.docker_container_exec:
#     container: nextcloud-mariadb
#     argv:
#       - mysqldump
#       - -u root
#       - "--password={{ nextcloud_mariadb_root_password }}"
#       - "{{ nextcloud_mariadb_database }} > /var/lib/mysql/backup.sql"
#   tags:
#     - never
#     - nextcloud-mariadb-backup
#     - backup

# - name: restore with mysqldum from backup
#   community.docker.docker_container_exec:
#     container: nextcloud-mariadb
#     argv:
#       - mysql
#       - -u root
#       - "--password={{ nextcloud_mariadb_root_password }}"
#       - "{{ nextcloud_mariadb_database }} < /var/lib/mysql/backup.sql"
#   tags:
#     - never
#     - nextcloud-mariadb-restore
#     - restore

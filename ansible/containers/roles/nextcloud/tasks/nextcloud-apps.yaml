# code: language=ansible
---
- name: install nextcloud apps
  community.docker.docker_container_exec:
    container: nextcloud
    user: www-data
    argv:
      - php
      - occ
      - app:install
      - "{{ item }}"
  loop:
    - contacts
    - calendar
    - deck
    - maps
    - notes
    - news
    - sociallogin
    - cookbook
    - bookmarks
  register: nextcloud_app_installed
  failed_when: nextcloud_app_installed.rc == 2
  changed_when: nextcloud_app_installed.rc == 0

- name: configure recognize app after installtion
  block:
    - name: install recognize app
      community.docker.docker_container_exec:
        container: nextcloud
        user: www-data
        argv:
          - php
          - occ
          - app:install
          - recognize
      register: recognize_app_installed
      failed_when: recognize_app_installed.rc == 2
      changed_when: recognize_app_installed.rc == 0

    - name: setting up recognize app
      when: recognize_app_installed.changed
      block:
        - name: download models
          community.docker.docker_container_exec:
            container: nextcloud
            user: www-data
            argv:
              - php
              - occ
              - recognize:download-models

        # values are taken from https://github.com/nextcloud/recognize/blob/main/lib/Service/SettingsService.php
        - name: enable recognize features
          community.docker.docker_container_exec:
            container: nextcloud
            user: www-data
            argv:
              - php
              - occ
              - config:app:set
              - recognize
              - "{{ item }}.enabled"
              - --value=true
          loop:
            - faces
            - imagenet
            - landmarks
            - geo

# code: language=ansible
#
# Create redis docker container for nextcloud
# Requires:
#   - nextcloud_network_name
---
- name: create directory for redis config
  become: true
  file:
    state: directory
    path: "{{ nextcloud_config_directory }}/redis"
    owner: "{{ ansible_user_id }}"
    group: "{{ ansible_user_id }}"

- name: copy redis configuration to nextcloud config directory
  template:
    src: redis.conf.j2
    dest: "{{ nextcloud_config_directory }}/redis/redis.conf"

- name: create a docker volume for redis
  docker_volume:
    name: nextcloud-redis-volume
    state: present

- name: create docker container for redis
  docker_container:
    name: nextcloud-redis
    image: "redis:{{ nextcloud_redis_version }}"
    state: started
    restart_policy: unless-stopped
    networks:
      - name: "{{ nextcloud_network_name | mandatory }}"
    volumes:
      - "{{ nextcloud_config_directory }}/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro"
      - nextcloud-redis-volume:/data
    command: redis-server /usr/local/etc/redis/redis.conf

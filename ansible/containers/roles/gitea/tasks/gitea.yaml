# code: language=ansible
#
# Setup gitea as a docker container
# Required arguments:
#   - gitea_network_name
#   - gitea_mysql_container_name
---
- name: create volumes for gitea
  docker_volume:
    name: "{{ item }}"
    state: present
  loop:
    - gitea-config-volume
    - gitea-data-volume

- name: create gitea docker container
  docker_container:
    name: gitea
    image: gitea/gitea:{{ gitea_version }}
    state: started
    restart_policy: unless-stopped
    networks:
      - name: "{{ traefik_proxy_network_name | mandatory }}"
      - name: "{{ gitea_network_name | mandatory }}"
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
      - gitea-config-volume:/etc/gitea
      - gitea-data-volume:/data
    env:
      USER_UID: "1000"
      USER_GID: "1000"
      START_SSH_SERVER: "true"
      SSH_LISTEN_PORT: "22"
      GITEA__server__SSH_DOMAIN: "{{ gitea_url }}"
      GITEA__server__DOMAIN: "{{ gitea_url }}"
      GITEA__server__ROOT_URL: "https://{{ gitea_url }}/"
      GITEA__server__SSH_PORT: "{{ traefik_ssh_port | string }}"
      GITEA__service__ENABLE_NOTIFY_MAIL: "true"
      GITEA__service__DISABLE_REGISTRATION: "true"
      GITEA__picture__DISABLE_GRAVATAR: "true"
      GITEA__openid__ENABLE_OPENID_SIGNIN: "false"
      GITEA__openid__ENABLE_OPENID_SIGNUP: "false"
      GITEA__oauth2_client__ENABLE_AUTO_REGISTRATION: "true"
      GITEA__oauth2_client__UPDATE_AVATAR: "true"
      GITEA__oauth2_client__REGISTER_EMAIL_CONFIRM: "true"
      GITEA__database__DB_TYPE: mysql
      GITEA__database__HOST: "{{ gitea_mysql_container_name | mandatory }}:3306"
      GITEA__database__NAME: "{{ gitea_mysql_database }}"
      GITEA__database__USER: "{{ gitea_mysql_user }}"
      GITEA__database__PASSWD: "{{ gitea_mysql_password }}"
      GITEA__mailer__ENABLED: "true"
      GITEA__mailer__FROM: "{{ smtp_username | mandatory }}"
      GITEA__mailer__PROTOCOL: "smtp+{{ smtp_encryption | mandatory }}"
      GITEA__mailer__SMTP_ADDR: "{{ smtp_host | mandatory }}"
      GITEA__mailer__SMTP_PORT: "{{ smtp_port | mandatory }}"
      GITEA__mailer__USER: "{{ smtp_username | mandatory }}"
      GITEA__mailer__PASSWD: "{{ smtp_password | mandatory }}"

    labels:
      backup.enable: "true"
      traefik.enable: "true"

      traefik.http.routers.gitea.entrypoints: web
      traefik.http.routers.gitea.rule: "Host(`{{ gitea_url }}`)"
      traefik.http.routers.gitea.middlewares: gitea-https

      traefik.http.routers.gitea-https.tls: "true"
      traefik.http.routers.gitea-https.tls.certresolver: letsencrypt
      traefik.http.routers.gitea-https.entrypoints: "web-secured"
      traefik.http.routers.gitea-https.rule: "Host(`{{ gitea_url }}`)"
      traefik.http.middlewares.gitea-https.redirectscheme.scheme: https
      traefik.http.services.gitea-https.loadbalancer.server.port: "3000"

      traefik.tcp.routers.gitea-ssh.rule: "HostSNI(`*`)"
      traefik.tcp.routers.gitea-ssh.entrypoints: "ssh"
      traefik.tcp.routers.gitea-ssh.service: "gitea-ssh-service"
      traefik.tcp.services.gitea-ssh-service.loadbalancer.server.port: "22"

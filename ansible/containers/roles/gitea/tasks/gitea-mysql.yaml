# code: language=ansible
#
# Setup mysql as a docker container
# Required arguments:
#   - gitea_network_name
#   - gitea_mysql_container_name
---
- name: create volume for mysql
  docker_volume:
    name: gitea-mysql-volume
    state: present

- name: install mysql
  docker_container:
    name: "{{ gitea_mysql_container_name | mandatory }}"
    image: mysql:{{ gitea_mysql_version }}
    state: started
    restart_policy: unless-stopped
    networks:
      - name: "{{ gitea_network_name | mandatory }}"
    command: --transaction-isolation=READ-COMMITTED --binlog-format=ROW
    volumes:
      - gitea-mysql-volume:/var/lib/mysql
    env:
      MYSQL_ROOT_PASSWORD: "{{ gitea_mysql_root_password }}"
      MYSQL_DATABASE: "{{ gitea_mysql_database }}"
      MYSQL_USER: "{{ gitea_mysql_user }}"
      MYSQL_PASSWORD: "{{ gitea_mysql_password }}"
    labels:
      backup.enable: "true"
      backup.mysql: "true"

# - name: make backup with mysqldump
#   community.docker.docker_container_exec:
#     container: "{{ gitea_mysql_container_name }}"
#     argv:
#       - mysqldump
#       - -u root
#       - "--password={{ gitea_mysql_root_password }}"
#       - "{{ gitea_mysql_database }} > /var/lib/mysql/backup.sql"
#   tags: 
#     - never
#     - gitea-mysql-backup
#     - backup

# - name: restore with mysqldum from backup
#   community.docker.docker_container_exec:
#     container: "{{ gitea_mysql_container_name }}"
#     argv:
#       - mysql
#       - -u root
#       - "--password={{ gitea_mysql_root_password }}"
#       - "{{ gitea_mysql_database }} < /var/lib/mysql/backup.sql"
#   tags:
#     - never
#     - gitea-mysql-restore
#     - restore

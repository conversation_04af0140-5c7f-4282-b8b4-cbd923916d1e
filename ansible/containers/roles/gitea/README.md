# Gitea

[Gitea](https://about.gitea.com/) is an open source, simple and lightweight solution for managing source code. More features and plugin can be discovered at [Awesome Gitea](https://gitea.com/gitea/awesome-gitea/).

| Environment     | URL                                                                                      |
| --------------- | ---------------------------------------------------------------------------------------- |
| local (Vagrant) | [https://gitea.reuchlinstrasse.duckdns.org/](https://gitea.reuchlinstrasse.duckdns.org/) |
| prod            | [https://gitea.catslab.duckdns.org/](https://gitea.catslab.duckdns.org/)                 |

## Installation

Gitea is installed via ansible role [gitea](../../playbooks/roles/gitea/tasks/main.yaml) and uses mysql as database solution

- [installation guide](https://docs.gitea.com/installation/install-with-docker-rootless/)

- [source code](https://github.com/go-gitea/gitea)
- [docker image](https://hub.docker.com/r/gitea/gitea)
- [mysql](https://hub.docker.com/_/mysql)

## Versions

| **Service** | **Version**                                                                                                                                       |
| ----------- | ------------------------------------------------------------------------------------------------------------------------------------------------- |
| Gitea       | [1.22.6](https://hub.docker.com/layers/gitea/gitea/1.22.6/images/sha256-dfc61e347c8b582df918f4556401bf2cecdfbdb56c5282ae9488dd76fca3e41c)         |
| mysql       | [8](https://hub.docker.com/layers/library/mysql/8/images/sha256-28a16e31b140d750048cd5fadcaed22ac08d0eeb18567f79f822aee1f237b43c?context=explore) |

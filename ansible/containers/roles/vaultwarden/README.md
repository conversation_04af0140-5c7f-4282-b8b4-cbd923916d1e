# Vaultwarden

[Vaultwarden](https://www.vaultwarden.net/) is an open source alternative to [Bitwarden](https://bitwarden.com/de-DE/) with all business features supported.

| Environment     | URL                                                                                                  |
| --------------- | ---------------------------------------------------------------------------------------------------- |
| local (Vagrant) | [https://vaultwarden.reuchlinstrasse.duckdns.org/](https://vaultwarden.reuchlinstrasse.duckdns.org/) |
| prod            | [https://vaultwarden.catslab.duckdns.org/](https://vaultwarden.catslab.duckdns.org/)                 |

## Installation

Vaultwarden is installed via ansible role [vaultwarden](../../playbooks/roles/vaultwarden/tasks/main.yaml).

- [mariadb](https://hub.docker.com/_/mariadb)

- [source code](https://github.com/dani-garcia/vaultwarden)
- [docker image](https://hub.docker.com/r/vaultwarden/server)
- [wiki](https://github.com/dani-garcia/vaultwarden/wiki)

## Versions

| Docker Image       | Version                                                                                                                                                        |
| ------------------ | -------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| mariadb            | [11](https://hub.docker.com/layers/library/mariadb/11/images/sha256-04d70a5a9b401d1513b2d03cc446b3a375f4b9ce583c727f7dce8b74b3fded94)                          |
| vaultwarden/server | [1.32.7-alpine](https://hub.docker.com/layers/vaultwarden/server/1.32.7-alpine/images/sha256-6710946d07e21f73aa4146dc8808be773f89efa3337d218e7cf7ca93b5097d16) |

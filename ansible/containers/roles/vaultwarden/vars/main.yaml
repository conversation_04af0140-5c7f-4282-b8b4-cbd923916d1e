# code: language=ansible
---
vaultwarden_mariadb_root_password: "{{ lookup('file', 'mariadb-root-password') }}"
vaultwarden_mariadb_password: "{{ lookup('file', 'mariadb-vaultwarden-password') }}"
vaultwarden_admin_password: "{{ lookup('file', 'vaultwarden-admin-password') }}"

vaultwarden_mariadb_hostname: mariadb
vaultwarden_mariadb_connection_string: "mysql://{{ vaultwarden_mariadb_user }}:{{ vaultwarden_mariadb_password }}@{{ vaultwarden_mariadb_hostname }}:3306/{{ vaultwarden_mariadb_database }}"

vaultwarden_network_name: vaultwarden-network
vaultwarden_mariadb_container_name: vaultwarden-mariadb

vaultwarden_installation_key: "{{ lookup('file', 'vaultwarden-installation-key') }}"
vaultwarden_installation_id: "{{ lookup('file', 'vaultwarden-installation-id') }}"
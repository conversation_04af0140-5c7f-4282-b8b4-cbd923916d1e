# code: language=ansible
#
# Create vaultwarden docker container
# Requires:
#   - vaultwarden_network_name
#   - vaultwarden_mariadb_container_name
---
- name: create volume for vaultwarden
  docker_volume:
    name: vaultwarden-volume
    state: present

- name: vaultwarden docker container
  docker_container:
    name: vaultwarden
    image: "vaultwarden/server:{{ vaultwarden_version }}"
    state: started
    restart_policy: unless-stopped
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - vaultwarden-volume:/data/
    networks:
      - name: "{{ traefik_proxy_network_name | mandatory }}"
      - name: "{{ vaultwarden_network_name | mandatory }}"
        links:
          - "{{ vaultwarden_mariadb_container_name | mandatory }}:{{ vaultwarden_mariadb_hostname }}"
    env:
      # ADMIN_TOKEN: "{{ vaultwarden_admin_password }}"
      SIGNUPS_ALLOWED: "false"
      INVITATIONS_ALLOWED: "false"
      DATABASE_URL: "{{ vaultwarden_mariadb_connection_string }}"
      DOMAIN: "https://{{ vaultwarden_url | mandatory }}"
      SMTP_HOST: "{{ smtp_host | mandatory }}"
      SMTP_FROM: "{{ smtp_username | mandatory }}"
      SMTP_PORT: "{{ smtp_port | mandatory }}"
      SMTP_SECURITY: "{{ smtp_encryption | default('starttls') }}"
      SMTP_USERNAME: "{{ smtp_username | mandatory }}"
      SMTP_PASSWORD: "{{ smtp_password | mandatory }}"
      PUSH_ENABLED: "true"
      PUSH_INSTALLATION_ID: "{{ vaultwarden_installation_id | mandatory }}"
      PUSH_INSTALLATION_KEY: "{{ vaultwarden_installation_key | mandatory }}"
      PUSH_RELAY_URI: "https://push.bitwarden.eu"
      PUSH_IDENTITY_URI: "https://identity.bitwarden.eu"

    labels:
      backup.enable: "true"
      traefik.enable: "true"

      traefik.http.routers.vaultwarden.entrypoints: web
      traefik.http.routers.vaultwarden.rule: "Host(`{{ vaultwarden_url | mandatory }}`)"
      traefik.http.routers.vaultwarden.middlewares: vaultwarden-https

      traefik.http.routers.vaultwarden-https.tls: "true"
      traefik.http.routers.vaultwarden-https.tls.certresolver: letsencrypt
      traefik.http.routers.vaultwarden-https.entrypoints: web-secured
      traefik.http.routers.vaultwarden-https.rule: "Host(`{{ vaultwarden_url | mandatory }}`)"
      traefik.http.middlewares.vaultwarden-https.redirectscheme.scheme: https
      traefik.http.services.vaultwarden-https.loadbalancer.server.port: "80"

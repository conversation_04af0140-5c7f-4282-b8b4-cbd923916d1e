# code: language=ansible
#
# Create mariadb docker container for vaultwarden
# Requires:
#   - vaultwarden_network_name
#   - vaultwarden_mariadb_container_name
---
- name: create docker volume for mariadb
  docker_volume:
    name: vaultwarden-mariadb-volume
    state: present

- name: mariadb docker container
  docker_container:
    name: "{{ vaultwarden_mariadb_container_name }}"
    image: "mariadb:{{ vaultwarden_mariadb_version }}"
    state: started
    restart_policy: unless-stopped
    networks:
      - name: "{{ vaultwarden_network_name | mandatory }}"
    volumes:
      - vaultwarden-mariadb-volume:/var/lib/mysql
      - /etc/localtime:/etc/localtime:ro
    env:
      MARIADB_ROOT_PASSWORD: "{{ vaultwarden_mariadb_root_password }}"
      MARIADB_DATABASE: "{{ vaultwarden_mariadb_database }}"
      MARIADB_USER: "{{ vaultwarden_mariadb_user }}"
      MARIADB_PASSWORD: "{{ vaultwarden_mariadb_password }}"

    labels:
      backup.enable: "true"
      backup.mysql: "true"

  register: mariadb_step

- pause:
    seconds: 5
  when: mariadb_step.changed

# - name: make backup with mysqldump
#   community.docker.docker_container_exec:
#     container: "{{ vaultwarden_mariadb_container_name }}"
#     argv:
#       - mysqldump
#       - -u root
#       - "--password={{ vaultwarden_mariadb_root_password }}"
#       - "{{ vaultwarden_mariadb_database }} > /var/lib/mysql/backup.sql"
#   tags:
#     - never
#     - vaultwarden-mariadb-backup
#     - backup

# - name: restore with mysqldum from backup
#   community.docker.docker_container_exec:
#     container: "{{ vaultwarden_mariadb_container_name }}"
#     argv:
#       - mysql
#       - -u root
#       - "--password={{ vaultwarden_mariadb_root_password }}"
#       - "{{ vaultwarden_mariadb_database }} < /var/lib/mysql/backup.sql"
#   tags:
#     - never
#     - vaultwarden-mariadb-restore
#     - restore

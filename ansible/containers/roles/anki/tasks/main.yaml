# code: language=ansible
---
- name: create config directory
  become: true
  file:
    state: directory
    path: "{{ anki_config_directory | mandatory }}"
    owner: "{{ ansible_user_id }}"
    group: "{{ ansible_user_id }}"
  tags:
    - anki

- name: copy docker file
  copy:
    src: Dockerfile
    dest: "{{ anki_config_directory }}/Dockerfile"
  tags:
    - anki

- name: build docker image
  docker_image:
    name: anki-sync-server
    tag: "{{ anki_version }}"
    source: build
    build:
      path: "{{ anki_config_directory }}"
      args:
        ANKI_VERSION: "{{ anki_version | mandatory }}"
  tags:
    - anki

- name: create docker volumes for anki
  docker_volume:
    name: "{{ item }}"
    state: present
  loop:
    - anki-volume
  tags:
    - anki

- name: set anki user
  set_fact:
    anki_env_vars: |
      {%- set envs = {} -%}
      {{ envs.update({ "SYNC_BASE": "/home/<USER>/sync" }) }}
      {%- if anki_is_password_hashed -%}
        {{ envs.update({"PASSWORDS_HASHED": anki_is_password_hashed}) }}
      {%- endif -%}
      {%- for user in anki_users -%}
        {{ envs.update({user.key: user.value}) }}
      {%- endfor -%}
      {{ envs }}
  tags:
    - anki

- name: star anki-sync-server
  docker_container:
    name: anki-sync-server
    image: "anki-sync-server:{{ anki_version }}"
    pull: false
    state: started
    restart_policy: unless-stopped
    volumes:
      - anki-volume:/home/<USER>/sync
    env: "{{ anki_env_vars | trim }}"

    labels:
      backup.enable: "true"
      traefik.enable: "true"

      traefik.http.routers.anki.entrypoints: web
      traefik.http.routers.anki.rule: "Host(`{{ anki_url | mandatory }}`)"
      traefik.http.routers.anki.middlewares: anki-https

      traefik.http.routers.anki-https.tls: "true"
      traefik.http.routers.anki-https.tls.certresolver: letsencrypt
      traefik.http.routers.anki-https.entrypoints: web-secured
      traefik.http.middlewares.anki-https.redirectscheme.scheme: https
      traefik.http.services.anki-https.loadbalancer.server.port: "8080"
      traefik.http.routers.anki-https.rule: "Host(`{{ anki_url }}`)"

  tags:
    - anki

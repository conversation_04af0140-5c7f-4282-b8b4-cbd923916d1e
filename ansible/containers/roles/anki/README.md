# Anki

| Environment     | URL                                                                                              |
| --------------- | ------------------------------------------------------------------------------------------------ |
| local (Vagrant) | [https://anki.reuchlinstrasse.duckdns.org/admin](https://anki.reuchlinstrasse.duckdns.org/admin) |
| prod            | [https://anki.catslab.duckdns.org/admin](https://anki.catslab.duckdns.org/admin)                 |

## Installation

Anki sync service is installed via ansible role [anki](./tasks/main.yaml).

- [source code](https://github.com/ankitects/anki/tree/main)
- [anki sync sever docs](https://docs.ankiweb.net/sync-server.html)
- [docker image](https://github.com/ankitects/anki/tree/main/docs/syncserver)

## Versions

| Docker image     | Version   |
| ---------------- | --------- |
| anki-sync-server | [24.04](https://github.com/ankitects/anki/releases/tag/24.04) |

> The version is the git tag

## Todo

- Anki seems not to work or the login is not working
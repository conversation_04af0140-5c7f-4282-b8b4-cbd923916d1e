# code: language=ansible
---
- name: create docker container for striling-pdf
  docker_container:
    name: stirling-pdf
    image: "frooodle/s-pdf:{{ stirling_pdf_version | mandatory }}"
    state: started
    restart_policy: unless-stopped
    networks:
      - name: "{{ traefik_proxy_network_name | mandatory }}"
    env:
      SYSTEM_DEFAULTLOCALE: "en-DE"
    labels:
      traefik.enable: "true"

      traefik.http.routers.stirlingpdf.entrypoints: web
      traefik.http.routers.stirlingpdf.rule: "Host(`{{ stirling_pdf_url | mandatory }}`)"
      traefik.http.routers.stirlingpdf.middlewares: stirlingpdf-https

      traefik.http.routers.stirlingpdf-https.tls.certresolver: letsencrypt
      traefik.http.routers.stirlingpdf-https.entrypoints: "web-secured"
      traefik.http.routers.stirlingpdf-https.rule: "Host(`{{ stirling_pdf_url }}`)"
      traefik.http.middlewares.stirlingpdf-https.redirectscheme.scheme: https
      traefik.http.services.stirlingpdf-https.loadbalancer.server.port: "8080"
  tags:
    - stirling-pdf
    - pdf

# Stirling-PDF

[Stirling-PDF](https://stirlingtools.com/) is locally hosted one-stop-shop for all your PDF needs.

| Environment     | URL                                                                                  |
| --------------- | ------------------------------------------------------------------------------------ |
| local (Vagrant) | [https://pdf.reuchlinstrasse.duckdns.org/](https://pdf.reuchlinstrasse.duckdns.org/) |
| prod            | [https://pdf.catslab.duckdns.org/](https://pdf.catslab.duckdns.org/)                 |

## Installation

Stirling-PDF is installed via ansible role [stirling-pdf](../../playbooks/roles/stirling-pdf/tasks/main.yaml).

- [source code](https://github.com/Stirling-Tools/Stirling-PDF)
- [docker image](https://hub.docker.com/r/frooodle/s-pdf)

## Versions

| Docker Image   | Version                                                                                                                                      |
| -------------- | -------------------------------------------------------------------------------------------------------------------------------------------- |
| frooodle/s-pdf | [0.36.5](https://hub.docker.com/layers/frooodle/s-pdf/0.36.5/images/sha256-295da4b4bafce65a9daff852233b9cda91141ebb3c7e959267f24f0ef0354dcd) |

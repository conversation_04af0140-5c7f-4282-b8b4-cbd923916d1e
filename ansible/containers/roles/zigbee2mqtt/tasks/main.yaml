# code: language=ansible
---
- name: create config directory for zigbee
  become: true
  file:
    state: directory
    path: "{{ zigbee2mqtt_config_directory }}"
    owner: "{{ ansible_user_id }}"
    group: "{{ ansible_user_id }}"
  tags:
    - zigbee2mqtt

- name: copy zigbee2mqtt config
  template:
    src: configuration.yaml.j2
    dest: "{{ zigbee2mqtt_config_directory }}/configuration.yaml"
  tags:
    - zigbee2mqtt

- name: create docker volume for zigbee2mqtt
  docker_volume:
    name: zigbee2mqtt-volume
    state: present
  tags:
    - zigbee2mqtt

- name: create docker container for zigbee2mqtt
  docker_container:
    name: zigbee2mqtt
    image: "koenkk/zigbee2mqtt:{{ zigbee2mqtt_version }}"
    state: started
    restart_policy: unless-stopped
    networks:
      - name: "{{ traefik_proxy_network_name | mandatory }}"
    volumes:
      - /run/udev:/run/udev:ro
      - zigbee2mqtt-volume:/app/data
      - "{{ zigbee2mqtt_config_directory }}/configuration.yaml:/app/data/configuration.yaml"
    devices:
      - "{{ zigbee2mqtt_mqtt_zigbee_device | mandatory }}:/dev/ttyACM0"
    env:
      TZ: Europe/Berlin
    labels:
      backup.enable: "true"
      traefik.enable: "true"

      traefik.http.routers.zigbeemqtt.entrypoints: web
      traefik.http.routers.zigbeemqtt.rule: "Host(`{{ zigbee2mqtt_url | mandatory }}`)"
      traefik.http.routers.zigbeemqtt.middlewares: zigbeemqtt-https

      traefik.http.routers.zigbeemqtt-https.tls.certresolver: letsencrypt
      traefik.http.routers.zigbeemqtt-https.entrypoints: web-secured
      traefik.http.routers.zigbeemqtt-https.rule: "Host(`{{ zigbee2mqtt_url }}`)"
      traefik.http.middlewares.zigbeemqtt-https.redirectscheme.scheme: https
      traefik.http.services.zigbeemqtt-https.loadbalancer.server.port: "8080"
  tags:
  - zigbee2mqtt

      
    
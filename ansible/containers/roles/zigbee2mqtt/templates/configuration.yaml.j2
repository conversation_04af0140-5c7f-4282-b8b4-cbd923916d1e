# Home Assistant integration (MQTT discovery)
homeassistant: false

# allow new devices to join
permit_join: false

# MQTT settings
mqtt:
  base_topic: {{ zigbee2mqtt_mqtt_base_topic }}
  server: {{ zigbee2mqtt_mqtt_host }}
  user: {{ zigbee2mqtt_mqtt_username}}
  password: {{ zigbee2mqtt_mqtt_password }}

# Serial settings
serial:
  # Location of CC2531 USB sniffer
  port: /dev/ttyACM0

frontend: true

# code: language=ansible
---
- name: create config directory for mosquitto
  become: true
  file:
    state: directory
    path: "{{ mosquitto_config_directory }}"
    owner: "{{ ansible_user_id }}"
    group: "{{ ansible_user_id }}"

- name: copy mosquitto config
  copy:
    src: mosquitto.conf
    dest: "{{ mosquitto_config_directory }}"

- name: create docker volumes do mosquitto
  docker_volume:
    name: "{{ item }}"
    state: present
  loop:
    - mosquitto-log-volume
    - mosquitto-data-volume

- name: create docker volumes do mosquitto
  docker_volume:
    name: mosquitto-etc-volume
    state: present
  register: mosquitto_etc_volume_created

- name: create passwd file for mosquitto otherwise container want start
  when: mosquitto_etc_volume_created.changed
  block:
    - name: create passwd file
      docker_container:
        name: mosquitto-passwd
        image: "eclipse-mosquitto:{{ mosquitto_version }}"
        state: started
        cleanup: true
        auto_remove: true
        entrypoint:
          - /bin/sh
          - -c
        command:
          [
            "touch /mosquitto/etc/mosquitto/passwd && chmod 0600 /mosquitto/etc/mosquitto/passwd",
          ]
        volumes:
          - mosquitto-etc-volume:/mosquitto/etc/mosquitto

    - pause:
        seconds: 5

- name: create docker container for mosquitto
  docker_container:
    name: mosquitto
    image: "eclipse-mosquitto:{{ mosquitto_version }}"
    state: started
    restart_policy: unless-stopped
    volumes:
      - mosquitto-etc-volume:/mosquitto/etc/mosquitto
      - mosquitto-log-volume:/mosquitto/log
      - mosquitto-data-volume:/mosquitto/data
      - "{{ mosquitto_config_directory }}/mosquitto.conf:/mosquitto/config/mosquitto.conf"
    networks:
      - name: "{{ traefik_proxy_network_name | mandatory }}"
    ports:
      - "1883:1883/tcp"
    labels:
      backup.enable: "true"
      traefik.enable: "true"

      traefik.http.routers.mosquitto.entrypoints: web-secured
      traefik.http.routers.mosquitto.tls.certresolver: letsencrypt
      traefik.http.routers.mosquitto.rule: "Host(`{{ mosquitto_url | mandatory }}`)"
      traefik.http.services.mosquitto.loadbalancer.server.port: "9001"

      traefik.tcp.routers.mosquitto.entrypoints: mqtt
      traefik.tcp.routers.mosquitto.tls: "true"
      traefik.tcp.routers.mosquitto.tls.certresolver: letsencrypt
      traefik.tcp.routers.mosquitto.rule: "HostSNI(`{{ mosquitto_url }}`)"
      traefik.tcp.services.mosquitto.loadbalancer.server.port: "1883"

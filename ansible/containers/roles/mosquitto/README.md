# Mosquitto

| Environment           | URL                                                                                      |
| --------------------- | ---------------------------------------------------------------------------------------- |
| local (Vagrant)       | [mqtt.reuchlinstrasse.duckdns.org](mqtt.reuchlinstrasse.duckdns.org)                     |
| mqttx local (Vagrant) | [https://mqttx.reuchlinstrasse.duckdns.org/](https://mqttx.reuchlinstrasse.duckdns.org/) |
| prod                  | [mqtt.catslab.duckdns.org](mqtt.catslab.duckdns.org)                                     |
| mqttx prod            | [https://mqttx.catslab.duckdns.org/](https://mqttx.catslab.duckdns.org/)                 |

## Installation

- [docker image](https://hub.docker.com/_/eclipse-mosquitto)
- [source code](https://github.com/eclipse/mosquitto)
- [docs](https://github.com/eclipse/mosquitto/tree/master/docker/2.0)

- [mqttx docker image](https://hub.docker.com/r/emqx/mqttx-web)
- [mqttx source code](https://github.com/emqx/MQTTX)
- [mqttx docs](https://mqttx.app/docs)

## Version

| Docker Image      | Version                                                                                                                                                      |
| ----------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| eclipse-mosquitto | [2.0](https://hub.docker.com/layers/library/eclipse-mosquitto/2.0/images/sha256-bade7465a128bc618d2f53e24f100c285c587a3b4b4b4e1cde056afdd4767a74)            |
| emqx/mqttx-web    | [v1.9.8](https://hub.docker.com/layers/emqx/mqttx-web/v1.9.8/images/sha256-c6fe16f28ecebfa59e70dfda736ec03cfd98def1de7bb24f12a2f9b073e82170?context=explore) |

## Users

Mosquitto allows users to authenticate with username and password. Let's assume you want to create a user `mqttx` run:

```shell
docker exec -it mosquitto mosquitto_passwd /mosquitto/etc/mosquitto/passwd mqttx
```

and pass the password you want to use for `mqttx`.

For this to work mosquitto.conf file needs to contain the following:

```shell
allow_anonymous false
password_file /mosquitto/etc/mosquitto/passwd
```

To delete user `mqttx` run:

```shell
mosquitto_passwd -D /mosquitto/etc/mosquitto/passwd mqttx
```

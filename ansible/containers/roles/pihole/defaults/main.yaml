# code: language=ansible
---
pihole_version: 2024.07.0
pihole_url: "pihole.{{ home_lab_base_url }}"
pihole_config_directory: /var/pihole

# unbound
pihole_use_unbound: true
pihole_unbound_version: 1.20.0

# dns settings
pihole_default_dns: *******
pihole_default_ipv6_dns: 2606:4700:4700::1111
pihole_dnssec_active: "{{ pihole_use_unbound }}"
pihole_dns_bogus_priv: true
pihole_dns_fqdn_required: true

# dhcp configuration
pihole_dhcp_active: false
pihole_dhcp_start_ip: ***********
pihole_dhcp_end_ip: *************
pihole_dhcp_router_ip: ***********
pihole_dhcp_lease_time: 168 # hours
pihole_dhcp_domain: "."
pihole_dhcp_rapid_commit: true
pihole_dhcp_ipv6: false

# Pihole

[Pihole](https://pi-hole.net/) is a network wide ad blocker

| Environment     | URL                                                                                                  |
| --------------- | ---------------------------------------------------------------------------------------------------- |
| local (Vagrant) | [https://pihole.reuchlinstrasse.duckdns.org/admin](https://pihole.reuchlinstrasse.duckdns.org/admin) |
| prod            | [https://pihole.catslab.duckdns.org/admin](https://pihole.catslab.duckdns.org/admin)                 |

## Installation

Pihole is installed as a docker container via ansible role [pihole](../../playbooks/roles/pihole/tasks/main.yaml)

- [source code](https://github.com/pi-hole/pi-hole)
- [docs](https://docs.pi-hole.net/)
- [docker docs](https://github.com/pi-hole/docker-pi-hole)
- [docker image](https://hub.docker.com/r/pihole/pihole)

`Pihole`, in default configuration, implements [unbound](https://nlnetlabs.nl/projects/unbound/about/) as a DNS cache. This can be disable by overriding the variable `pihole_use_unbound` and set it to false. `Pihole` will automatically use [Cloudflare](https://www.cloudflare.com/de-de/) as default dns. This behavior can be controlled by overriding default config

| Variable                  | Default value        |
| ------------------------- | -------------------- |
| `pihole_default_dns`      | *******              |
| `pihole_default_ipv6_dns` | 2606:4700:4700::1111 |

> You can find other upstream DNS [here](https://docs.pi-hole.net/guides/dns/upstream-dns-providers/)

A list of available blocklists can be found on [Github](https://github.com/RPiList/specials/blob/54876178ffa7e4d1224ac81b00bedd0040f65802/Blocklisten.md) or visit [Github Blocklist Project](https://github.com/blocklistproject/Lists)

### Unbound

By setting `pihole_use_unbound` to `true` `Pihole` will spin up a unbound docker container and use it as DNS.

- [source code](https://github.com/MatthewVance/unbound-docker/blob/master/README.md)
- [pihole unbound docs](https://docs.pi-hole.net/guides/dns/unbound/)
- [docker image](https://hub.docker.com/r/mvance/unbound)

`Unbound` will use `Cloudflare` as default forwarder but other forwarder can be configured. Check out the [default configuration](https://github.com/MatthewVance/unbound-docker/blob/master/1.19.0/data/opt/unbound/etc/unbound/forward-records.conf#L11-L12).

### DHCP

`Pihole` is also able to run with a `DHCP` server enabled by setting `pihole_dhcp_active` to `true`.
Due to broadcast limitations Pihole must run either in `network_mode`: `host` or have a some sort dhcp-replay configured which forward all DHCP messaged to `Pihole` docker container

Due to the decision to use `Traefik` as reverse proxy I have decided to run a [dhcp relay](../../playbooks/roles/pihole/tasks/dhcp-relay.yaml) which has `network_mode` set to `host` and there is no need to map port `67/tcp` in docker.

## Version

| Docker Image   | Version                                                                                                                                                           |
| -------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| pihole/pihole  | [2024.07.0](https://hub.docker.com/layers/pihole/pihole/2024.07.0/images/sha256-e53305e9e00d7ac283763ca9f323cc95a47d0113a1e02eb9c6849f309d6202dd?context=explore) |
| mvance/unbound | [1.20.0](https://hub.docker.com/layers/mvance/unbound/1.20.0/images/sha256-4bf67b567f392956455bd4f8a4cdd48010e234f1a07c0a99c2cff2ddbb3e8a7a?context=explore)      |

## dnscrypt-proxy vs Unbound

[dnscrypt-proxy](https://github.com/DNSCrypt/dnscrypt-proxy) does ruffly the same as unbound and is considered to be a drop in replacement for providing `DNSSEC`. At the moment I don't see any reasons to replace `unbound` by `dnscrypt-proxy` and will stick to `unbound` for now.


# code: language=ansible
#
# Start unbound as a docker container
# Requires:
#   - pihole_network_name
#   - pihole_unbound_docker_ip
---
- name: create config directory for unbound
  become: true
  file:
    state: directory
    path: "{{ pihole_config_directory }}/unbound"
    owner: "{{ ansible_user_id }}"
    group: "{{ ansible_user_id }}"

- name: copy template into config directory
  template:
    src: srv-records.conf.j2
    dest: "{{ pihole_config_directory }}/unbound/srv-records.conf"

- name: create unbound docker container
  docker_container:
    name: pihole-unbound
    image: "mvance/unbound:{{ pihole_unbound_version }}"
    state: started
    restart_policy: unless-stopped
    networks:
      - name: "{{ pihole_network_name | mandatory }}"
        ipv4_address: "{{ pihole_unbound_docker_ip | mandatory }}"
    dns_servers:
      - "{{ pihole_default_dns | mandatory }}"
    volumes:
      - "{{ pihole_config_directory }}/unbound/srv-records.conf:/opt/unbound/etc/unbound/srv-records.conf"

# code: language=ansible
#
# Due to broadcast limitations we need to implement a DHCP relay
# to be able to forward DHCP messages to Pihole
# Requires:
#   - pihole_docker_ip
---
- name: create config directory for dhcp-relay
  become: true
  file:
    path: "{{ pihole_config_directory }}/dhcp-relay"
    state: directory
    owner: "{{ ansible_user_id }}"
    group: "{{ ansible_user_id }}"

- name: copy dhcp-relay Dockerfile to host
  copy:
    src: dhcp-relay.Dockerfile
    dest: "{{ pihole_config_directory }}/dhcp-relay"

- name: build dhcp-relay docker image
  docker_image:
    name: dhcp-relay:v1.0.0-alpine
    state: present
    source: build
    build:
      path: "{{ pihole_config_directory }}/dhcp-relay"
      dockerfile: dhcp-relay.Dockerfile

- name: create docker container for dhcp relay
  docker_container:
    name: pihole-dhcp-relay
    image: dhcp-relay:v1.0.0-alpine
    state: started
    restart_policy: unless-stopped
    capabilities:
      - NET_ADMIN
    network_mode: host
    command: "-s {{ pihole_docker_ip | mandatory }}"

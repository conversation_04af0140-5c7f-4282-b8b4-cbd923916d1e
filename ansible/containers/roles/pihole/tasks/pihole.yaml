# code: language=ansible
#
# Create and start docker container for pihole
# and provide all necessary configuration for auto detected features from variables
#
# Requires:
#   - pihole_network_name
#   - pihole_docker_ip
#   - pihole_unbound_docker_ip
---
- name: create docker volume for pihole
  docker_volume:
    name: "{{ item }}"
    state: present
  loop:
    - pihole-volume
    - pihole-dnsmasq-volume

# if `pihole_dhcp_active` is set to `true` create a directory for to store
# config files and copy dhcp related configs into the created directory
- name: set up config directory and prepare config files
  when: pihole_dhcp_active
  block:
    - name: create pihole config directory
      become: true
      file:
        state: directory
        path: "{{ pihole_config_directory }}"
        owner: "{{ ansible_user_id }}"
        group: "{{ ansible_user_id }}"

    - name: copy dhcp config
      template:
        src: 04-dhcp-options.conf.j2
        dest: "{{ pihole_config_directory }}/04-dhcp-options.conf"

# auto detect features and create based on enabled features a dns config
# for pihole which is later passed to `PIHOLE_DNS_` env var
# if `pihole_default_dns` default gateway is taken (to avoid default config which is google dns)
# if there is `pihole_default_ipv6_dns` provided then create a semicolon separated list
# like `pihole_default_dns`;`pihole_default_ipv6_dns`.
- set_fact:
    pihole_dns: |
      {%- if pihole_use_unbound is true -%}
        {{ pihole_unbound_docker_ip }}
      {%- elif pihole_default_dns is not none -%}
        {%- set pihole_dns = [pihole_default_dns, pihole_default_ipv6_dns] -%}
        {{ pihole_dns | select() | join(';') }}
      {%- else -%}
        {{ network_gateway }}
      {%- endif -%}

# append `pihole_network_name` if provided
- set_fact:
    pihole_networks: |
      {%- set networks = [] -%}
      {%- set traefik_network = { 'name': traefik_proxy_network_name | mandatory } -%}
      {{ networks.append(traefik_network) }}
      {%- if pihole_network_name | trim | length > 1 -%}
        {%- set pihole_network = { 'name': pihole_network_name | trim } -%}
        {{ networks.append(pihole_network) }}
      {%- endif -%}
      {{ networks }}

# if `pihole_dhcp_active` is set to `true` a `dnsmasq` config needs to be mounted
# with updated host ip. Checkout `04-dhcp-options.conf.j2` for more information
- set_fact:
    pihole_volumes: |
      {%- set volumes = [] -%}
      {{ volumes.append('pihole-volume:/etc/pihole') }}
      {{ volumes.append('pihole-dnsmasq-volume:/etc/dnsmasq.d') }}
      {%- if pihole_dhcp_active is true -%}
        {{ volumes.append("{{ pihole_config_directory }}/04-dhcp-options.conf:/etc/dnsmasq.d/04-dhcp-options.conf:ro") }}
      {%- endif -%}
      {{ volumes }}

- name: create pihole docker container
  docker_container:
    name: pihole
    image: "pihole/pihole:{{ pihole_version }}"
    state: started
    restart_policy: unless-stopped
    hostname: pihole
    capabilities:
      - NET_ADMIN
    networks: "{{ pihole_networks | trim }}"
    volumes: "{{ pihole_volumes | trim }}"
    ports:
      - "53:53/tcp"
      - "53:53/udp"
    dns_search_domains:
      - "."
    dns_servers:
      - 127.0.0.1
    env:
      TZ: Europe/Berlin
      WEBPASSWORD: "{{ pihole_admin_password }}"
      VIRTUAL_HOST: pihole
      # Set to your server's LAN IP, used by web block modes.
      FTLCONF_LOCAL_IPV4: "{{ ansible_host }}"
      # `all` permits listening on internet origin subnets in addition to local
      DNSMASQ_LISTENING: all

      REV_SERVER: "{{ (not pihole_dhcp_active) | ternary('true', 'false') | string }}"
      REV_SERVER_CIDR: "{{ ansible_default_ipv4.network + '/' + network_netmask_prefix }}"
      REV_SERVER_TARGET: "{{ network_gateway }}"

      # DNS setting
      PIHOLE_DNS_: "{{ pihole_dns | trim }}"
      DNSSEC: "{{ pihole_dnssec_active | ternary('true', 'false') | string }}"
      DNS_BOGUS_PRIV: "{{ pihole_dns_bogus_priv | ternary('true', 'false') | string }}"
      DNS_FQDN_REQUIRED: "{{ pihole_dns_fqdn_required | ternary('true', 'false') | string }}"

      # DHCP settings
      DHCP_ACTIVE: "{{ pihole_dhcp_active | ternary('true', 'false') | string }}"
      DHCP_START: "{{ pihole_dhcp_start_ip }}"
      DHCP_END: "{{ pihole_dhcp_end_ip }}"
      DHCP_ROUTER: "{{ pihole_dhcp_router_ip }}"
      DHCP_LEASETIME: "{{ pihole_dhcp_lease_time | string }}"
      PIHOLE_DOMAIN: "{{ pihole_dhcp_domain }}"
      DHCP_rapid_commit: "{{ pihole_dhcp_rapid_commit | ternary('true', 'false') | string  }}"
      DHCP_IPv6: "{{ pihole_dhcp_ipv6 | ternary('true', 'false') | string }}"

    labels:
      backup.enable: "true"
      traefik.enable: "true"

      traefik.http.routers.pihole.entrypoints: web
      traefik.http.routers.pihole.rule: "Host(`{{ pihole_url | mandatory }}`)"
      traefik.http.routers.pihole.middlewares: pihole-https

      traefik.http.routers.pihole-https.tls: "true"
      traefik.http.routers.pihole-https.tls.certresolver: letsencrypt
      traefik.http.routers.pihole-https.entrypoints: web-secured
      traefik.http.middlewares.pihole-https.redirectscheme.scheme: https
      traefik.http.services.pihole-https.loadbalancer.server.port: "80"
      traefik.http.routers.pihole-https.rule: "Host(`{{ pihole_url | mandatory }}`)"

# code: language=ansible
---
- name: create network for pihole
  when: pihole_dhcp_active or pihole_use_unbound
  tags:
    - pihole
  docker_network:
    name: "{{ pihole_network_name }}"
    state: present
    ipam_config:
      - subnet: **********/16
        gateway: **********

- import_tasks: unbound.yaml
  when: pihole_use_unbound
  tags:
    - pihole
    - unbound

- import_tasks: dhcp-relay.yaml
  when: pihole_dhcp_active
  tags:
    - pihole
    - pihole-dhcp
    - dhcp-relay
    - dhcp

- import_tasks: pihole.yaml
  tags:
    - pihole

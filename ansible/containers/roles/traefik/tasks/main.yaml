# code: language=ansible
---
- name: create directory to store traefik config
  become: true
  file:
    state: directory
    path: "{{ traefik_config_dir }}"
    owner: "{{ ansible_user_id }}"
    group: "{{ ansible_user_id }}"
  tags:
    - traefik

- name: copy traefik config
  template:
    src: "{{ item }}.j2"
    dest: "{{ traefik_config_dir }}/{{ item }}"
    backup: true
  loop:
    - traefik.yaml
    - traefik-dynamic.yaml
  tags:
    - traefik

- name: create docker volume to store traefik data
  docker_volume:
    name: traefik-volume
    state: present
  tags:
    - traefik

- name: create traefik proxy network
  docker_network:
    name: "{{ traefik_proxy_network_name }}"
    state: present
  tags:
    - traefik

- set_fact:
    traefik_ports: |
      {%- set ports = [] -%}
      {{ ports.append('443:443/tcp') }}
      {{ ports.append('80:80/tcp') }}
      {%- if traefik_enable_ssh_forwarding is true -%}
        {{ ports.append("{{ traefik_ssh_port }}:{{ traefik_ssh_port }}/tcp") }}
      {%- endif -%}
      {%- if traefik_enable_mqtt_forwarding is true -%}
        {{ ports.append('8883:8883/tcp') }}
      {%- endif -%}
      {%- if traefik_enable_websocket_forwarding is true -%}
        {{ ports.append('8083:8083/tcp') }}
      {%- endif -%}
      {{ ports }}
  tags:
    - traefik

- name: traefik docker container
  tags:
    - traefik
  docker_container:
    name: traefik
    image: "traefik:{{ traefik_version }}"
    state: started
    restart_policy: unless-stopped
    networks:
      - name: "{{ traefik_proxy_network_name | mandatory }}"
    ports: "{{ traefik_ports | trim }}"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik-volume:/data
      - "{{ traefik_config_dir }}/traefik.yaml:/etc/traefik/traefik.yml:ro"
      - "{{ traefik_config_dir }}/traefik-dynamic.yaml:/etc/traefik/traefik-dynamic.yaml:ro"
    env:
      DUCKDNS_TOKEN: "{{ traefik_duckdns_token | mandatory }}"
    labels:
      backup.enable: "true"

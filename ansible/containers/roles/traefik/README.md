# Traefik

- [source code](https://github.com/traefik/traefik)
- [docker image](https://hub.docker.com/_/traefik)
- [docs](https://doc.traefik.io/traefik/)
- [awesome traefik](https://github.com/traefik/traefik/wiki/Awesome-Traefik)

## Versions

| Docker Image | Version                                                                                                                                                         |
| ------------ | --------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| traefik      | [v2.11.0](https://hub.docker.com/layers/library/traefik/v2.11.0/images/sha256-903f89ddacc3059657565d3e5b3b8e8dcba4cc3b8af08183dad6fdc415459111?context=explore) |


## DNS

[DuckDNS](https://www.duckdns.org/)

[Certs](https://doc.traefik.io/traefik/https/acme/#tlschallenge)

[Let's encrypt PROD](https://community.letsencrypt.org/t/acme-v2-production-environment-wildcards/55578)
[Let's encrypt STAGE](https://community.letsencrypt.org/t/staging-endpoint-for-acme-v2/49605)
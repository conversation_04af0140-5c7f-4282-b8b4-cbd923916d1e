entryPoints:
{% if traefik_enable_mqtt_forwarding is true %}
  mqtt:
    address: ":8883"
{% endif %}

{% if traefik_enable_ssh_forwarding is true %}
  ssh:
    address: ":{{ traefik_ssh_port }}"
{% endif %}

  web:
    address: ":80"
    http:
      redirections:
        entryPoint:
          to: web-secured
          scheme: https
          permanent: true

  web-secured:
    address: ":443"
    http:
      tls:
        certResolver: letsencrypt
        domains:
          - main: "{{ traefik_base_url | mandatory }}"
          - sans:
              - "*.{{ traefik_base_url | mandatory }}"
providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    network: "{{ traefik_proxy_network_name | mandatory }}"
  file:
    filename: /etc/traefik/traefik-dynamic.yaml

certificatesResolvers:
  letsencrypt:
    acme:
      email: "{{ traefik_acme_email | mandatory}}"
      storage: /data/acme.json
      caServer: "{{ traefik_acme_ca_server | mandatory }}"
      dnsChallenge:
        provider: duckdns
        delayBeforeCheck: 0

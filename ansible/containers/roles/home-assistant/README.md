# Home Assistant

| Environment     | URL                                                                                              |
| --------------- | ------------------------------------------------------------------------------------------------ |
| local (Vagrant) | [https://assistant.reuchlinstrasse.duckdns.org/](https://assistant.reuchlinstrasse.duckdns.org/) |
| prod            | [https://assistant.catslab.duckdns.org/](https://assistant.catslab.duckdns.org/)                 |

## Installation

- [docker image](https://github.com/home-assistant/core/pkgs/container/home-assistant)
- [installation instructions](https://www.home-assistant.io/installation/alternative)

After the Docker container has been created `/config/configuration.yaml` needs to be adjusted to enable reverse proxy setting:

```yaml
http:
  server_port: 8123
  use_x_forwarded_for: true
  trusted_proxies:
    - <traefik-ip>
```

> where `<traefik-ip>` is the ip assigned by docker (eg. ***********)

## Version

| Docker image   | Version                                                                                                   |
| -------------- | --------------------------------------------------------------------------------------------------------- |
| home-assistant | [2024.12.5](https://github.com/home-assistant/core/pkgs/container/home-assistant/326238240?tag=2024.12.5) |

# code: language=ansible
---
- name: create config directory for home-assistant
  become: true
  file:
    state: directory
    path: "{{ home_assistant_config_directory }}"
    owner: "{{ ansible_user_id }}"
    group: "{{ ansible_user_id }}"
  tags:
    - home-assistant

- name: find ip of reverse proxy
  shell:
    cmd: sh -c "docker inspect -f '{{ '{{' }}json (index .IPAM.Config 0).Subnet{{ '}}' }}' {{ traefik_proxy_network_name }} | sed 's/\"//g'"
  register: reverse_proxy_ip
  changed_when: false
  tags:
    - home-assistant

- name: copy configuration file
  template:
    src: configuration.yaml.j2
    dest: "{{ home_assistant_config_directory }}/configuration.yaml"
  vars:
    traefik_ip: "{{ reverse_proxy_ip.stdout }}"
  tags:
    - home-assistant

- name: create docker volume for home-assistant config
  docker_volume:
    name: home-assistant-config-volume
    state: present
  tags:
    - home-assistant

- name: create docker container for home-assistant
  docker_container:
    name: home-assistant
    image: "ghcr.io/home-assistant/home-assistant:{{ home_assistant_version }}"
    state: started
    privileged: true
    restart_policy: unless-stopped
    volumes:
      - home-assistant-config-volume:/config
      - /etc/localtime:/etc/localtime:ro
      - "{{ home_assistant_config_directory }}/configuration.yaml:/config/configuration.yaml"
    networks:
      - name: "{{ traefik_proxy_network_name | mandatory }}"
    devices:
      - "{{ home_assistant_zigbee_device | mandatory }}:/dev/ttyACM0"
    env:
      TZ: Europe/Berlin
    labels:
      backup.enable: "true"
      traefik.enable: "true"

      traefik.http.routers.homeassistant.entrypoints: web
      traefik.http.routers.homeassistant.rule: "Host(`{{ home_assistant_url | mandatory }}`)"
      traefik.http.routers.homeassistant.middlewares: homeassistant-https

      traefik.http.routers.homeassistant-https.tls: "true"
      traefik.http.routers.homeassistant-https.tls.certresolver: letsencrypt
      traefik.http.routers.homeassistant-https.entrypoints: "web-secured"
      traefik.http.routers.homeassistant-https.rule: "Host(`{{ home_assistant_url }}`)"
      traefik.http.middlewares.homeassistant-https.redirectscheme.scheme: https
      traefik.http.services.homeassistant-https.loadbalancer.server.scheme: "http"
      traefik.http.services.homeassistant-https.loadbalancer.server.port: "8123"

  tags:
    - home-assistant

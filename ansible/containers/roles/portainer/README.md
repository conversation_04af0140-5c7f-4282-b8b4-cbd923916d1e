# Portainer

[Portainer](https://www.portainer.io/) provides a simple UI for managing docker container. The license for up to three nodes is free to use and suitable for any simple home-lab.

| Environment     | URL                                                                                              |
| --------------- | ------------------------------------------------------------------------------------------------ |
| local (Vagrant) | [https://portainer.reuchlinstrasse.duckdns.org/](https://portainer.reuchlinstrasse.duckdns.org/) |
| prod            | [https://portainer.catslab.duckdns.org/](https://portainer.catslab.duckdns.org/)                 |

## Installation

Portainer is installed via ansible [role](../../playbooks/roles/portainer/tasks/main.yaml) and does not require any other dependencies and therefore it is easy to use.

- [setup guide](https://docs.portainer.io/start/install-ce/server/setup)
- [docker image - DEV](https://hub.docker.com/r/portainer/portainer-ce)
- [docker image - PROD](https://hub.docker.com/r/portainer/portainer-ee)

## Versions

| Docker Image           | Version                                                                                                                                                                            |
| ---------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| portainer/portainer-ee | [2.20.0-alpine](https://hub.docker.com/layers/portainer/portainer-ee/2.20.0-alpine/images/sha256-584a321a792942622473382965272ff8ef7a2cd32026ec2a4adbeb749ade3eb3?context=explore) |
| portainer/portainer-ce | [2.20.0-alpine](https://hub.docker.com/layers/portainer/portainer-ce/2.20.0-alpine/images/sha256-6f8c80f1ac8fb39268960b9adfea1936ba6f0a6d3ac54d739206168866bd32a4?context=explore) |

## License

Portainer license is store as an [ansible vault file](../../playbooks/roles/portainer/files/portainer-license) and can be viewed with:

```shell
ansible-vault view --vault-password-file=<path_to_password> playbooks/roles/portainer/files/portainer-license
```

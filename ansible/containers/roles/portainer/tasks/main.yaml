# code: language=ansible
---
- name: create recommended volume
  docker_volume:
    name: portainer-volume
    state: present
  tags:
    - portainer

- set_fact:
    portainer_docker_image: |
      {%- if ansible_user == "vagrant" -%}
        portainer-ce
      {%- else -%}
        portainer-ee
      {%- endif -%}
  tags:
    - portainer

- name: install portainer
  tags:
    - portainer
  docker_container:
    name: portainer
    image: "portainer/{{portainer_docker_image | trim}}:{{ portainer_version }}"
    state: started
    restart_policy: unless-stopped
    networks:
      - name: "{{ traefik_proxy_network_name | mandatory }}"
    volumes:
      - portainer-volume:/data
      - /var/run/docker.sock:/var/run/docker.sock
    labels:
      backup.enable: "true"
      traefik.enable: "true"

      traefik.http.routers.portainer.entrypoints: web
      traefik.http.routers.portainer.rule: "Host(`{{ portainer_url }}`)"
      traefik.http.routers.portainer.middlewares: portainer-https

      traefik.http.routers.portainer-https.tls: "true"
      traefik.http.routers.portainer-https.tls.certresolver: letsencrypt
      traefik.http.routers.portainer-https.entrypoints: "web-secured"
      traefik.http.routers.portainer-https.rule: "Host(`{{ portainer_url }}`)"
      traefik.http.middlewares.portainer-https.redirectscheme.scheme: https
      traefik.http.services.portainer-https.loadbalancer.server.port: "9000"

# Zitadel

[Zitadel](https://zitadel.com/) is a open source identity provider self-hosted in docker

| Environment     | URL                                                                                          |
| --------------- | -------------------------------------------------------------------------------------------- |
| local (Vagrant) | [https://zitadel.reuchlinstrasse.duckdns.org/](https://zitadel.reuchlinstrasse.duckdns.org/) |
| prod            | [https://zitadel.catslab.duckdns.org/](https://zitadel.catslab.duckdns.org/)                 |

## Installation

- [source code](https://github.com/zitadel/zitadel)
- [cockroach source code](https://github.com/cockroachdb/cockroach)
- [zitadel docker image](https://github.com/zitadel/zitadel/pkgs/container/zitadel)
- [cockroachdb docker image](https://hub.docker.com/r/cockroachdb/cockroach)
- [cockroachdb installation instructions](https://www.cockroachlabs.com/docs/stable/start-a-local-cluster-in-docker-linux)

## Versions

| Docker Image          | Version                                                                                                                                                                 |
| --------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| zitadel/zitadel       | [v2.42.17](https://github.com/zitadel/zitadel/pkgs/container/zitadel/196415732?tag=v2.42.17)                                                                              |
| cockroachdb/cockroach | [v23.1.13](https://hub.docker.com/layers/cockroachdb/cockroach/v23.1.13/images/sha256-f0c3295eb2e226fae50bbf5118cc432889dd0fbae639d5143b4ef88775de9283?context=explore) |

# Use-full links

#### Zitadel

- [zitadel loadbalancing-example](https://zitadel.com/docs/self-hosting/deploy/loadbalancing-example)
- [configure zitadel](https://zitadel.com/docs/self-hosting/manage/configure)
- [zitadel traefik](https://doc.traefik.io/traefik/user-guides/grpc/#with-http-h2c)

#### Cockroach

- [cluster setup troubleshooting](https://www.cockroachlabs.com/docs/stable/cluster-setup-troubleshooting)
- [common errors](https://www.cockroachlabs.com/docs/stable/common-errors)

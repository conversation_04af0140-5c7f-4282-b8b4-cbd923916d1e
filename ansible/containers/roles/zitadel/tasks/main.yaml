# code: language=ansible
#
# https://zitadel.com/docs/self-hosting/deploy/loadbalancing-example
---
- name: create docker volume to store shared certs
  docker_volume:
    name: "{{ zitadel_certs_volume_name | mandatory }}"
    state: present
  tags:
    - zitadel

- name: create docker network for zitadel
  docker_network:
    name: "{{ zitadel_network_name | mandatory }}"
    state: present
  tags:
    - zitadel

- import_tasks: cockroachdb.yaml
  tags:
    - zitadel

- import_tasks: zitadel.yaml
  tags:
    - zitadel

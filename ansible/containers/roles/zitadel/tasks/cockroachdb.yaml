# code: language=ansible
#
# Run cockroachdb as docker container
# Requires:
#   - zitadel_network_name
#   - zitadel_cockroachdb_container_name
#   - zitadel_certs_volume_name
---
- name: create volume for cockroachdb
  docker_volume:
    name: zitadel-cockroachdb-volume
    state: present

- name: create docker volume to store cockroach certs
  docker_volume:
    name: zitadel-cockroachdb-certs-volume
    state: present

- name: create cockroachdb docker container
  register: zitadel_cockroachdb
  docker_container:
    name: "{{ zitadel_cockroachdb_container_name | mandatory }}"
    image: "cockroachdb/cockroach:{{ zitadel_cockroachdb_version | mandatory }}"
    state: started
    restart_policy: unless-stopped
    command: "start-single-node --advertise-addr {{ zitadel_cockroachdb_container_name }}"
    networks:
      - name: "{{ zitadel_network_name | mandatory }}"
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - zitadel-cockroachdb-volume:/cockroach/cockroach-data
      - zitadel-cockroachdb-certs-volume:/cockroach/certs
    healthcheck:
      interval: 10s
      timeout: 30s
      retries: 5
      start_period: 20s
      test:
        - CMD
        - curl
        - -f
        - http://localhost:8080/health?ready=1
    labels:
      backup.enable: "true"

- name: copy certs from cockroachdb to zitadel
  when: zitadel_cockroachdb.changed
  block:
    - pause:
        seconds: 15

    - name: start docker container to copy certs
      register: zitadel_cockroachdb_certs
      docker_container:
        name: zitadel-cockroachdb-certs
        image: "cockroachdb/cockroach:{{ zitadel_cockroachdb_version }}"
        state: started
        cleanup: true
        auto_remove: true
        entrypoint:
          - /bin/bash
          - -c
        command:
          [
            "cp /certs/* /zitadel-certs/ && cockroach cert create-client --overwrite --certs-dir /zitadel-certs/ --ca-key /zitadel-certs/ca.key zitadel_user && chown 1000:1000 /zitadel-certs/*",
          ]
        volumes:
          - zitadel-cockroachdb-certs-volume:/certs:ro
          - "{{ zitadel_certs_volume_name | mandatory }}:/zitadel-certs"

    - pause:
        seconds: 15

# code: language=ansible
#
# Run `Zitadel` as docker container
# Requires:
#   - zitadel_network_name
#   - zitadel_cockroachdb_container_name
#   - zitadel_certs_volume_name
---
- name: create config directory for Zitadel
  become: true
  file:
    state: directory
    path: "{{ zitadel_config_directory }}"
    owner: "{{ ansible_user_id }}"
    group: "{{ ansible_user_id }}"

- name: copy config files to config directory
  copy:
    src: "{{ item }}"
    dest: "{{ zitadel_config_directory }}/{{ item }}"
  loop:
    - zitadel-config.yaml
    - zitadel-init-steps.yaml
    - zitadel-secrets.yaml

- name: create zitadel docker container
  docker_container:
    name: zitadel
    image: "ghcr.io/zitadel/zitadel:{{ zitadel_version | mandatory }}"
    state: started
    restart_policy: unless-stopped
    command: 'start-from-init --tlsMode external --masterkey "{{ zitadel_master_key | mandatory }}" --config /zitadel-config.yaml --config /zitadel-secrets.yaml --steps zitadel-init-steps.yaml'
    networks:
      - name: "{{ traefik_proxy_network_name | mandatory }}"
      - name: "{{ zitadel_network_name | mandatory }}"
    volumes:
      - "{{ zitadel_config_directory }}/zitadel-config.yaml:/zitadel-config.yaml:ro"
      - "{{ zitadel_config_directory }}/zitadel-init-steps.yaml:/zitadel-init-steps.yaml:ro"
      - "{{ zitadel_config_directory }}/zitadel-secrets.yaml:/zitadel-secrets.yaml:ro"
      - "{{ zitadel_certs_volume_name | mandatory }}:/crdb-certs:ro"
    env:
      ZITADEL_DATABASE_COCKROACH_HOST: "{{ zitadel_cockroachdb_container_name | mandatory }}"
      ZITADEL_EXTERNALPORT: "443"
      ZITADEL_EXTERNALSECURE: "true"
      ZITADEL_TLS_ENABLED: "false"
      ZITADEL_EXTERNALDOMAIN: "{{ zitadel_url | mandatory }}"
      ZITADEL_LOGSTORE_ACCESS_STDOUT_ENABLED: "true"
      ZITADEL_FIRSTINSTANCE_ORG_HUMAN_USERNAME: "<EMAIL>"
      ZITADEL_FIRSTINSTANCE_ORG_HUMAN_PASSWORD: "Password1!"
    healthcheck:
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
      test:
        - CMD
        - /app/zitadel
        - ready

    labels:
      backup.enable: "true"
      traefik.enable: "true"

      traefik.http.routers.zitadel.entrypoints: web
      traefik.http.routers.zitadel.rule: "Host(`{{ zitadel_url }}`)"
      traefik.http.routers.zitadel.middlewares: zitadel-https

      traefik.http.routers.zitadel-https.tls: "true"
      traefik.http.routers.zitadel-https.tls.certresolver: letsencrypt
      traefik.http.routers.zitadel-https.entrypoints: web-secured
      traefik.http.routers.zitadel-https.rule: "Host(`{{ zitadel_url }}`)"
      traefik.http.middlewares.zitadel-https.redirectscheme.scheme: https
      traefik.http.services.zitadel-https.loadbalancer.passHostHeader: "true"
      traefik.http.services.zitadel-https.loadbalancer.server.scheme: "h2c"
      traefik.http.services.zitadel-https.loadbalancer.server.port: "8080"

      traefik.http.routers.zitadel-https.middlewares: zitadel-headers
      traefik.http.middlewares.zitadel-headers.headers.isDevelopment: "false"
      traefik.http.middlewares.zitadel-headers.headers.allowedHosts: "{{ zitadel_url }}"
      traefik.http.middlewares.zitadel-headers.headers.customRequestHeaders.authority: "{{ zitadel_url }}"

# code: language=ansible
---
- name: create docker container for pairdrop
  docker_container:
    name: pairdrop
    image: "lscr.io/linuxserver/pairdrop:{{ pairdrop_version }}"
    state: started
    restart_policy: unless-stopped
    networks:
      - name: "{{ traefik_proxy_network_name | mandatory }}"
    env:
      PUID: "1000"
      GUID: "1000"
      TZ: Europe/Berlin
      RATE_LIMIT: "false"
    labels:
      traefik.enable: "true"

      traefik.http.routers.pairdrop.entrypoints: web
      traefik.http.routers.pairdrop.rule: "Host(`{{ pairdrop_url }}`)"
      traefik.http.routers.pairdrop.middlewares: pairdrop-https

      traefik.http.routers.pairdrop-https.tls: "true"
      traefik.http.routers.pairdrop-https.tls.certresolver: letsencrypt
      traefik.http.routers.pairdrop-https.entrypoints: "web-secured"
      traefik.http.routers.pairdrop-https.rule: "Host(`{{ pairdrop_url }}`)"
      traefik.http.middlewares.pairdrop-https.redirectscheme.scheme: https
      traefik.http.services.pairdrop-https.loadbalancer.server.port: "3000"
  tags:
    - pairdrop

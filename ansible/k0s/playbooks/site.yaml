# code: language=ansible
#
# Requirements:
# 
# install kubectl locally to interact with kubernetes cluster
# https://kubernetes.io/de/docs/tasks/tools/install-kubectl/
#
# usage: ansible-playbook --vault-password-file=~/.ansible-vault.key playbooks/k0s.playbook.yaml --limit debian

---
- name: Install k0s
  hosts: all
  gather_facts: true

  pre_tasks:
    - name: update all packages before running any other task
      become: true
      package:
        update_cache: true
        upgrade: "dist"
        autoremove: yes
        autoclean: yes

  tasks:
    - name: install curl if curl is missing
      become: true
      package:
        name: curl
        state: latest

    - name: install k0s
      become: true
      shell: |
        curl -sSLf https://get.k0s.sh | sudo sh
        k0s install controller --single
        k0s start

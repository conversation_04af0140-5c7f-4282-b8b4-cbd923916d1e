# code: language=ansible
#
# usage: ansible-playbook ansible/core/playbook.yaml -e host=debian
---
- name: Install core packages
  hosts: "{{ host }}"
  gather_facts: true
  become: true

  pre_tasks:
    - name: update all packages before running any other task
      package:
        update_cache: true
        upgrade: "dist"
        autoremove: yes
        autoclean: yes

  roles:
    - dropbear
    - ssh
    - docker
    - backup
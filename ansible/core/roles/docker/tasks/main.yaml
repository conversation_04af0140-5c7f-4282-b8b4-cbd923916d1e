# code: language=ansible
#
# Install docker from script by using this documentations
# https://docs.docker.com/engine/install/debian/#install-using-the-convenience-script
---
- name: check if docker is installed
  shell: docker --version
  ignore_errors: true
  register: docker_installed
  changed_when: docker_installed.rc > 0
  tags:
    - docker

- name: install docker from script
  when: docker_installed is failed
  tags:
    - docker
  block:
    - name: install dependencies
      become: true
      package:
        state: latest
        update_cache: true
        name: curl

    - name: install docker
      become: true
      shell: curl -fsSL get.docker.com | sh

    - name: remove packages
      become: true
      package:
        state: absent
        name: curl

    - name: create user for docker
      when: docker_append_current_user_to_group
      become: true
      user:
        state: present
        name: "{{ ansible_user }}"
        append: true
        groups:
          - docker

- import_tasks: install-compose-plugin.yaml
  when: docker_install_docker_compose
  tags:
    - docker

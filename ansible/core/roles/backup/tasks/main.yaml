# code: language=ansible
---
- name: first check if backup directory exists
  stat:
    path: "{{ backup_target_director }}"
  register: backup_director_exists

- name: handle missing backup directory
  when: not backup_director_exists.stat.exists
  block:
    - fail:
        msg: "Make sure that '{{ backup_target_director }}' exists before running this role"
      when: not backup_autocreate_backup_directory

    - name: "create {{ backup_target_director }} for storing any backups"
      when: backup_autocreate_backup_directory
      become: true
      file:
        state: directory
        path: "{{ backup_target_director }}"
        owner: "{{ ansible_user_id }}"
        group: "{{ ansible_user_id }}"

- name: create directory to store backup scripts
  become: true
  file:
    state: directory
    path: "{{ backup_script_directory }}"
    owner: "{{ ansible_user_id }}"
    group: "{{ ansible_user_id }}"

- name: copy all backup scripts
  template:
    src: "{{ item }}.j2"
    dest: "{{ backup_script_directory }}/{{ item }}"
    mode: 0755
  loop:
    - backup-volumes.sh
    - docker-backup.sh
    - restore-volume.sh
    - sql-backup.sh

- name: create cron job to run backup script
  cron:
    name: docker backup
    hour: "4"
    minute: "0"
    job: "{{backup_script_directory}}/docker-backup.sh backup --all"

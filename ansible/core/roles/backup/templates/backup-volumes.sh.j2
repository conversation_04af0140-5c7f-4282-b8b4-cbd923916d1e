#!/bin/bash

VOLUMES_BACKUP_DIR={{ backup_target_director }}
{% raw %}
CONTAINER_NAME=$1

if [ -z $CONTAINER_NAME ]; then
    echo -e "\033[0;31mError\033[0m: missing container name."
    exit 1
fi

while [[ $# -gt 0 ]]; do
    case $1 in
        -o|--output)
            VOLUMES_BACKUP_DIR=$2
            shift
            shift
        ;;
        -*|--*)
            echo "Unknown option $1"
            exit 1
        ;;
        *)
            shift
        ;;
    esac
done

TODAY=$(date +"%Y-%m-%d")
TIMESTAMP=$(date +"%Y-%m-%d_%H:%M:%S")
CONTAINER_BACKUP_PATH="$VOLUMES_BACKUP_DIR/$TODAY/$CONTAINER_NAME"

mkdir -p "$CONTAINER_BACKUP_PATH"

for VOLUME_INFO in `docker inspect --format='{{ range .Mounts }}{{if and (eq .Type "volume") (eq .Mode "rw") }}{{print  .Name ";" .Destination " "}}{{end}}{{ end }}' $CONTAINER_NAME`
do    
    VOLUME_NAME=$(cut -d';' -f1 <<<"$VOLUME_INFO")
    VOLUME_PATH=$(cut -d';' -f2 <<<"$VOLUME_INFO")
    
    echo -n "$CONTAINER_NAME: backing up volume '$VOLUME_NAME' to '$CONTAINER_BACKUP_PATH'"
    
    WAS_RUNNING=false
    if [ $(docker inspect -f '{{.State.Running}}' $CONTAINER_NAME) = "true" ]; then
        WAS_RUNNING=true
        docker stop $CONTAINER_NAME 1> /dev/null
    fi

    docker run --rm --volumes-from $CONTAINER_NAME -v $CONTAINER_BACKUP_PATH:/backup alpine tar cf /backup/$VOLUME_NAME--$TIMESTAMP.tar $VOLUME_PATH 2>/dev/null

    if [ $WAS_RUNNING = true ]; then
        docker restart $CONTAINER_NAME 1> /dev/null
    fi
    
    echo -e " - \033[0;32mOk\033[0m"
done
{% endraw %}
#!/bin/bash

VOLUMES_BACKUP_DIR={{ backup_target_director }}
{% raw %}
KEEP_BACKUPS=3

usage() {
    cat <<EOF
Backup and restore volumes for given docker container. This scipt will stop given docker container
and automatically determine mounted writeable volumes and create a archive in $VOLUMES_BACKUP_DIR
like:
    $VOLUMES_BACKUP_DIR/<today>/<container_name>/<volume-name>-<timestap>.tar.gz

Usage:
    macking back
    ./backup-volumes.sh backup CONTAINER_ID [flags]

    backup all containers
    ./backup-volumes.sh backup --all [flags]

    restore volume for given container
    ./backup-volumes.sh restore CONTAINER_ID $VOLUMES_BACKUP_DIR/<today>/<container_name>/<volume-name>-<timestap>.tar.gz

    restore all volumes for given container
    ./backup-volumes.sh restore --all CONTAINER_ID
EOF
}

backup_all() {
    # before shutting down the container run sql-backup
    for CONTAINER in `docker inspect --format='{{.Name}}' $(docker ps -aq --filter "label=backup.mysql=true") | cut -f2 -d\/`
    do
{% endraw %}
        source {{ backup_script_directory }}/sql-backup.sh $CONTAINER $@
{% raw %}
    done

    for CONTAINER in `docker inspect --format='{{.Name}}' $(docker ps -aq --filter "label=backup.enable=true") | cut -f2 -d\/`
    do
{% endraw %}
        source {{ backup_script_directory }}/backup-volumes.sh $CONTAINER $@
{% raw %}
    done
}

remove_oldes_backups() {
    SORTED_BACKUPS=($(ls -t $VOLUMES_BACKUP_DIR));
    ELEMENT_COUNT=${#SORTED_BACKUPS[@]}
    
    for BACKUP_FOLDER in ${SORTED_BACKUPS[@]:${KEEP_BACKUPS}:${ELEMENT_COUNT}}
    do
        echo "removing $VOLUMES_BACKUP_DIR/$BACKUP_FOLDER"
        rm -rf $VOLUMES_BACKUP_DIR/$BACKUP_FOLDER
    done
    
}
backup() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -a|--all)
                backup_all ${@:2}
                remove_oldes_backups
                exit 0
            ;;
            *)
{% endraw %}
                source {{ backup_script_directory }}/backup-volumes.sh $@
{% raw %}
                exit 0
            ;;
        esac
    done
}

restore_all() {
    CONTAINER_NAME=$1
    YOUNGES_BACKUP=$(ls -t $VOLUMES_BACKUP_DIR | head -n 1)
    CONTAINER_VOLUMES=$VOLUMES_BACKUP_DIR/$YOUNGES_BACKUP/$CONTAINER_NAME
    
    if [ ! -d $CONTAINER_VOLUMES ]; then
        echo -e "\033[0;31mError\033[0m: container $CONTAINER_NAME does not exists in $VOLUMES_BACKUP_DIR/$YOUNGES_BACKUP."
    fi
    
    for VOLUME in `ls $CONTAINER_VOLUMES | grep volume`
    do
{% endraw %}
        source {{ backup_script_directory }}/restore-volume.sh $CONTAINER_NAME $CONTAINER_VOLUMES/$VOLUME
{% raw %}
    done
}

restore() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -a|--all)
                restore_all ${@:2}
                exit 0
            ;;
            *)
{% endraw %}
                source {{ backup_script_directory }}/restore-volume.sh $@
{% raw %}
                exit 0
            ;;
        esac
    done
}
case $1 in
    backup) backup ${@:2};;
    restore) restore ${@:2};;
esac
{% endraw %}
#!/bin/bash

VOLUMES_BACKUP_DIR={{ backup_target_director }}
{% raw %}
CONTAINER_NAME=$1
RESTORE_VOLUMES_FILE=$2

if [ -z $CONTAINER_NAME ]; then
    echo -e "\033[0;31mError\033[0m: missing container name."
    exit 1
fi

if [[ -z $RESTORE_VOLUMES_FILE || ! -f $RESTORE_VOLUMES_FILE ]]; then
    echo -e "\033[0;31mError\033[0m: missing file to restore from or cannot locate file: $RESTORE_VOLUMES_FILE"
    exit 1
fi

if [ -z `docker ps -aq --filter name=$CONTAINER_NAME` ]; then
    echo -e "\033[0;31mError\033[0m: container is missing. Make sure the container is created or is runnning."
    exit 1
fi

# check if the container was running previously
WAS_RUNNING=false
if [ $(docker inspect -f '{{.State.Running}}' $CONTAINER_NAME) = "true" ]; then
    WAS_RUNNING=true
    docker stop $CONTAINER_NAME 1> /dev/null
fi

VOLUME_NAME=$(basename $RESTORE_VOLUMES_FILE | sed -E 's/--(.*)//')
DOCKER_INSPECT_VOLUME=$(echo "docker inspect --format='{{ range .Mounts }}{{if eq .Name \"VOLUME_NAME\" }}{{ .Destination }}{{end}}{{ end }}' $CONTAINER_NAME" | sed -e s/VOLUME_NAME/$VOLUME_NAME/)
MOUNT_DESTINATION=$(eval $DOCKER_INSPECT_VOLUME)

if [ -z $MOUNT_DESTINATION ]; then
    echo -e "\033[0;31mError\033[0m: looks like $VOLUME_NAME does not exists."
    exit 1
fi

# restore backup
echo -n "$CONTAINER_NAME: restoring volume '$VOLUME_NAME' from '$RESTORE_VOLUMES_FILE'"

docker run --rm --volumes-from $CONTAINER_NAME -v $VOLUMES_BACKUP_DIR:/backup alpine sh -c "cd $MOUNT_DESTINATION && tar xf $RESTORE_VOLUMES_FILE --strip 1"

echo -e " - \033[0;32mOk\033[0m"

# start container in case it was running before
if [ $WAS_RUNNING = true ]; then
    docker restart $CONTAINER_NAME 1> /dev/null
fi
{% endraw %}
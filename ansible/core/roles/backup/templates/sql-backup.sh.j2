#!/bin/bash

VOLUMES_BACKUP_DIR={{ backup_target_director }}
{% raw %}
CONTAINER_NAME=$1
TODAY=$(date +"%Y-%m-%d")
TIMESTAMP=$(date +"%Y-%m-%d_%H:%M:%S")
CONTAINER_BACKUP_PATH="$VOLUMES_BACKUP_DIR/$TODAY/$CONTAINER_NAME"

sql_backup() {
    local DATABASE=$1;
    local PASSWORD=$2;
    local OUTPUT_FILENAME="$CONTAINER_BACKUP_PATH/backup-$TIMESTAMP.sql";
    
    
    echo -n "$CONTAINER_NAME: making sql dump from $DATABASE";
    
    # first make a sqldump
    docker exec -it $CONTAINER_NAME mysqldump -u root --password=$PASSWORD $DATABASE > $OUTPUT_FILENAME;
    
    echo -e " - \033[0;32mOk\033[0m";
    
}

main() {    
    if [ -z $CONTAINER_NAME ]; then
        echo -e "\033[0;31mError\033[0m: missing container name."
        exit 1
    fi
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -o|--output)
                VOLUMES_BACKUP_DIR=$2
                shift
                shift
            ;;
            -*|--*)
                echo "Unknown option $1"
                exit 1
            ;;
            *)
                shift
            ;;
        esac
    done
    
    local MYSQL_ROOT_PASSWORD=$(docker exec $CONTAINER_NAME /usr/bin/env | grep _ROOT_PASSWORD | awk -F '=' '{print $2}');
    local MYSQL_DATABASE=$(docker exec $CONTAINER_NAME /usr/bin/env | grep _DATABASE | awk -F '=' '{print $2}');
    
    sql_backup $MYSQL_DATABASE $MYSQL_ROOT_PASSWORD;
}

main $@
{% endraw %}
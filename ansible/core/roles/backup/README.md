# Backups

Backups and restores are made with dockers `--volumes-from` ([more details](https://docs.docker.com/storage/volumes/#back-up-restore-or-migrate-data-volumes)). Mounted named volumes are compressed and exported as `*.tar.gz` file.
All backups are stores in `/backup` directory and `3` backups are kept. Backups are made every day at `04:00` at the morning.

## Backup script

For backing up volumes a new script was implemented. The script stops the container, makes a backup of all mounted volumes and is started afterwards.

```shell
./docker-backup.sh backup nextcloud
```

> the script is located at `/opt/scripts/backup`.

By setting `--output` the backup target directory can be overridden.

```shell
./docker-backup.sh backup nextcloud --output ./another-destination-for-backup
```

> if the passed directory does not exists, it will be created.

To backup all docker container there is a `--all` flag.

```shell
./docker-backup.sh backup --all
```

> All flags are combinable.

## Restore

Some script as used for backup is used to restore previously backed up volumes

```shell
./docker-backup.sh restore nextcloud /path-to-backup/nextcloud-volume--timestamp.tar.gz
```

to restore all available volumes in `/backup/<youngest-folder>/<container-name>/` a `--all` flag can be passed right after `restore` option

```shell
./docker-backup.sh restore --all nextcloud
```
<br />

## MySQL and MariaDB

For backing up and restoring sql database there steps implemented which are not running as default and only tags are set. Sql dumps are made with `mysqldump`.

In Addition the a label `backup.mysql` can be set to `true` for nightly backups with `mysqldump`. In this case a sql file will be created in the corresponding folder structure by running the following docker command

```shell
docker exec -it <CONTAINER_NAME> mysqldump -u root --password=<PASSWORD> <DATABASE> > /backup/<TODAY>/<CONTAINER_NAME>/backup-<TIMESTAMP>.sql;
```
Let's take `gitea-mysql` container for example then the command is looking like the following

```shell
docker exec -it gitea-mysql mysqldump -u root --password=<PASSWORD> gitea > /backup/2024-04-01/gitea-mysql/backup-2024-04-01_19:47:41.sql;
```

### Restore from sql backup file

To the restore from previously backed up database is as simple as running the `mysql` cli in the respective docker container.

```shell
docker exec -it <CONTAINER_NAME> mysql -u root -p <PASSWORD> < /backup/<TODAY>/<CONTAINER_NAME>/backup-<TIMESTAMP>.sql;
```
It would look like the following if `gitea-mysql` the target of restore:

```shell
docker exec -it gitea-mysql mysql -u root -p <PASSWORD> < /backup/2024-04-01/gitea-mysql/backup-2024-04-01_19:47:41.sql;
```
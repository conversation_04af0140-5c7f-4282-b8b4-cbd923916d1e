# code: language=ansible
#
# Verification:
#
# ping hostname.local
# avahi-resolve -n hostname.local
# avahi-browse -at
---
- name: Install required packages
  package:
    name:
      - avahi-daemon
      - avahi-utils
      - libnss-mdns
    state: present
    update_cache: yes
  
- name: Ensure hostname is set correctly
  hostname:
    name: "{{ hostname }}"
    
- name: Configure Avahi daemon
  template:
    src: avahi-daemon.conf.j2
    dest: /etc/avahi/avahi-daemon.conf
    owner: root
    group: root
    mode: '0644'
  notify: Restart Avahi
    
- name: Enable and start Avahi service
  systemd:
    name: avahi-daemon
    state: started
    enabled: yes
    
- name: Configure NSS for mDNS resolution
  lineinfile:
    path: /etc/nsswitch.conf
    regexp: '^hosts:'
    line: 'hosts:      files mdns4_minimal [NOTFOUND=return] dns mdns4'
    
- name: Open firewall ports for mDNS (UFW)
  ufw:
    rule: allow
    port: 5353
    proto: udp
  ignore_errors: yes
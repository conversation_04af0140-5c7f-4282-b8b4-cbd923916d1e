# code: language=ansible
#
# Tasks based on this tutorial:
# https://www.cyberciti.biz/security/how-to-unlock-luks-using-dropbear-ssh-keys-remotely-in-linux/
#
# This script is also capable to handle multiple encrypted drive
# by assign that the password used to encrypt the drives is always the same
#
# usage: ansible-playbook playbooks/beardrop.playbook.yaml -e host=debian
---
- name: find network interface
  when: not wifi_use_wifi
  shell:
    cmd: "/sbin/ip route | grep {{ ansible_host }} | awk '{ print($3) }'"
  register: network_interface

- name: install dropbear-initramfs
  become: true
  package:
    name: dropbear-initramfs
    state: latest

- name: copy dropbear config
  template:
    src: dropbear.conf.j2
    dest: /etc/dropbear/initramfs/dropbear.conf

- name: copy SSH key
  copy:
    src: ~/.ssh/id_rsa.pub
    dest: /etc/dropbear/initramfs/authorized_keys

- name: patch initramfs and append static ip config
  lineinfile:
    path: /etc/initramfs-tools/initramfs.conf
    regex: "IP="
    line: "{{ lookup('template', 'initramfs.conf.j2') }}"
  vars:
    dropbear_static_ip: "{{ dropbear_ssh_ip }}"
    dropbear_hostname: "{{ inventory_hostname }}"
    dropbear_device: "{{ network_interface.stdout }}"

- name: find the amount of encrypted drives based on their names
  shell:
    cmd: lsblk | grep crypt | wc -l
  register: total_encrypted_drives

# The script used to cache the password to encrypt the drives
# is based on keyutils and it seem like this dependency is missing
# on Debian 12
- name: setup keyutils to cache password for other drives
  when: (total_encrypted_drives.stdout | int) > 1
  block:
    - name: install keyutils if missing
      package:
        name: keyutils
        state: latest

    - name: patch /etc/crypttab to cache password. Assuming the password is always the same
      shell: |
        cp /etc/crypttab /etc/crypttab.bak
        sed -i s/$/,keyscript=decrypt_keyctl/ /etc/crypttab
      notify: update initramfs

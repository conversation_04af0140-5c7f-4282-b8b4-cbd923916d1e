# code: language=ansible
---
- name: Install tp-link-archer-t3u-plus driver
  gather_facts: true
  become: true

  pre_tasks:
    - name: update all packages before running any other task
      package:
        update_cache: true
        upgrade: "dist"
        autoremove: yes
        autoclean: yes

  tasks:
    - name: check if driver already installed
      ignore_errors: true
      shell:
        cmd: modprobe 88x2bu
      register: driver_installed
      changed_when: driver_installed.rc > 0
      tags:
        - tp-link-archer-t3u-plus

    - name: Install driver
      when: driver_installed is failed
      block:
        - name: install necessary dependencies
          apt:
            state: latest
            update_cache: true
            pkg:
              - git
              - build-essential
              - dkms
          tags:
            - tp-link-archer-t3u-plus

        - name: check if git respository is already clone
          stat:
            path: "{{ ansible_user_dir }}/rtl88x2bu"
          register: respository_exists
          tags:
            - tp-link-archer-t3u-plus

        - name: clone repository
          when: not respository_exists.stat.exists
          git:
            clone: true
            repo: https://github.com/cilynx/rtl88x2bu.git
            dest: "{{ ansible_user_dir }}/rtl88x2bu"
          tags:
            - tp-link-archer-t3u-plus

        - name: find module version
          shell:
            chdir: "{{ ansible_user_dir }}/rtl88x2bu"
            cmd: sed -n 's/\PACKAGE_VERSION="\(.*\)"/\1/p' dkms.conf
          register: module_version
          tags:
            - tp-link-archer-t3u-plus

        - name: build driver
          shell:
            chdir: "{{ ansible_user_dir }}/rtl88x2bu"
            cmd: |
              rsync -rvhP ./ /usr/src/rtl88x2bu-${VER}
              dkms add -m rtl88x2bu -v ${VER}
              dkms build -m rtl88x2bu -v ${VER}
              dkms install -m rtl88x2bu -v ${VER}
          environment:
            VER: "{{ module_version['stdout'] }}"
          tags:
            - tp-link-archer-t3u-plus

        - name: remove repository
          file:
            path: "{{ ansible_user_dir }}/rtl88x2bu"
            state: absent
          when: respository_exists.stat.exists
          tags:
            - tp-link-archer-t3u-plus

        - name: remove packages
          apt:
            state: absent
            pkg:
              - git
              - build-essential
              - dkms
          tags:
            - tp-link-archer-t3u-plus

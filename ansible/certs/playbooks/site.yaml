# code: language=ansible
#
# Usage:
# Generate and sign certificate for specified service:
# ansible-playbook generate-certs.yaml --tags=generate-service-cert -e service_url=vaultwarden.local -e service=vaultwarden --vault-password-file=~/.ansible-vault.key
---
- name: Generate certificates
  hosts: localhost
  become: false
  gather_facts: false

  vars:
    service: traefik
    service_path: "roles/{{ service }}/files"
    service_url: "{{ base_url }}"

  tasks:
    - name: "create folder {{ certs_dir }}"
      file:
        path: "{{ certs_dir }}"
        state: directory

    - name: Generate root CA
      tags:
        - root-ca
      block:
        - name: Generate the key for the root CA certificate
          command: "openssl genrsa -aes256 -out {{ certs_dir }}/{{ ca_cert_name }}.key -passout stdin 4096"
          args:
            stdin: "{{ passphrase }}"

        - name: Generate the self signed root CA certificate
          command: 'openssl req -x509 -new -nodes -key {{ certs_dir }}/{{ ca_cert_name }}.key -sha256 -days {{ expires_in }} -out {{ certs_dir }}/{{ ca_cert_name }}.crt -subj "/C=DE/ST=Baden-Wuerttemberg/O=Nikolaj Hartung/CN={{ base_url }}" -passin stdin'
          args:
            stdin: "{{ passphrase }}"

        - name: Convert to PEM format
          command: "openssl x509 -in {{ certs_dir }}/{{ ca_cert_name }}.crt -out {{ certs_dir }}/{{ ca_cert_name }}.pem -outform PEM"

    - name: Generate wildcard certificate for Traefik
      tags:
        - traefik
        - generate-service-cert
      block:
        - name: Create wildcard certificate for Traefik
          command: 'openssl req -new -nodes -out {{ service_path }}/{{ service_url }}.crt -newkey rsa:4096 -keyout {{ service_path }}/{{ service_url }}.key -passin stdin -subj "/C=DE/ST=Baden-Wuerttemberg/O=Nikolaj Hartung/CN=*.{{ service_url }}"'
          args:
            stdin: "{{ passphrase }}"

        - name: Make config for SAN
          template:
            src: "./cert.conf.j2"
            dest: "./cert.conf"

        - name: Sign wildcard certificate for Traefik with root CA
          command: "openssl x509 -req -in {{ service_path }}/{{ service_url }}.crt -CA {{ certs_dir }}/{{ ca_cert_name }}.crt -CAkey {{ certs_dir }}/{{ ca_cert_name }}.key -CAcreateserial -out {{ service_path }}/{{ service_url }}.crt -days {{ expires_in }} -sha256 -extfile cert.conf -passin stdin"
          args:
            stdin: "{{ passphrase }}"

        - name: Remove config
          file:
            path: "./cert.conf"
            state: absent

- name: Install CA cert locally
  hosts: localhost
  become: true
  gather_facts: false
  tasks:
    - name: Copy root CA into anchors
      when: install_ca
      copy:
        src: "{{ certs_dir }}/{{ ca_cert_name }}.pem"
        dest: "/etc/pki/ca-trust/source/anchors/{{ ca_cert_name }}.pem"
      tags:
        - root-ca

    - name: update ca trust
      when: install_ca
      command: update-ca-trust
      tags:
        - root-ca
